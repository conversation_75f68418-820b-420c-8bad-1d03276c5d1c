import LoginModal from '@/components/LoginModal';
import MainNavbar from '@/components/MainNavbar';
import SignupModal from '@/components/SignupModal';
import VideoPlayer from '@/components/VideoPlayer';
import { useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, Text, View } from 'react-native';

function formatTime(seconds: number) {
  const m = Math.floor(seconds / 60).toString().padStart(2, '0');
  const s = Math.floor(seconds % 60).toString().padStart(2, '0');
  return `${m}:${s}`;
}

export default function CourseDetails() {
  const [showLogin, setShowLogin] = useState(false);
  const [showSignup, setShowSignup] = useState(false);
  const { title } = useLocalSearchParams<{ title?: string }>();

  return (
    <>
      <LoginModal open={showLogin} onClose={() => setShowLogin(false)} />
      <SignupModal open={showSignup} onClose={() => setShowSignup(false)} />
      <MainNavbar onShowLogin={() => setShowLogin(true)} onShowSignup={() => setShowSignup(true)} />
      
      <ScrollView style={styles.container} contentContainerStyle={{ paddingBottom: 32 }}>
        {/* Course Header */}
        <View style={styles.header}>
          <Text style={styles.title}>{title || 'Course Details'}</Text>
          <Text style={styles.subtitle}>
            Master the fundamentals with our comprehensive video lessons
          </Text>
        </View>

        {/* Video Player */}
        <View style={styles.videoSection}>
          <VideoPlayer
            uri="https://www.learningcontainer.com/wp-content/uploads/2020/05/sample-mp4-file.mp4"
            height={Platform.OS === 'web' ? 400 : 250}
            style={styles.videoPlayer}
          />
        </View>

        {/* Course Info */}
        <View style={styles.courseInfo}>
          <Text style={styles.courseTitle}>Introduction to Entrepreneurship</Text>
          <Text style={styles.courseDescription}>
            Learn the essential skills needed to start and grow your business. 
            This comprehensive course covers everything from idea validation to scaling your startup.
          </Text>
          
          <View style={styles.statsRow}>
            <View style={styles.stat}>
              <Text style={styles.statNumber}>4.8</Text>
              <Text style={styles.statLabel}>Rating</Text>
            </View>
            <View style={styles.stat}>
              <Text style={styles.statNumber}>12.5k</Text>
              <Text style={styles.statLabel}>Students</Text>
            </View>
            <View style={styles.stat}>
              <Text style={styles.statNumber}>8h</Text>
              <Text style={styles.statLabel}>Duration</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 32,
    paddingBottom: 24,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
  },
  title: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1976d2',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  videoSection: {
    backgroundColor: '#000',
    marginVertical: 0,
  },
  videoPlayer: {
    borderRadius: 0,
  },
  courseInfo: {
    padding: 20,
    backgroundColor: '#fff',
    margin: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  courseTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111',
    marginBottom: 12,
  },
  courseDescription: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e5e5',
  },
  stat: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1976d2',
  },
  statLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
}); 
