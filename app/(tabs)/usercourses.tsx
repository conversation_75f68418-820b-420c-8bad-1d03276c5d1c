import LoginModal from '@/components/LoginModal';
import MainNavbar from '@/components/MainNavbar';
import SignupModal from '@/components/SignupModal';
import { useRouter } from 'expo-router';
import React, { useState } from 'react';
import { Animated, Easing, Image, NativeScrollEvent, NativeSyntheticEvent, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';

type Level = 'Discover' | 'Scale' | 'Grow';
interface Topic { name: string; learners: number; }
interface Course {
  title: string;
  topic: string;
  instructors: string;
  rating: number;
  ratingsCount: number;
  price: string;
  badge: string;
  image: { uri: string };
}

export default function UserCourses() {
  const router = useRouter();
  // Placeholder user data
  const user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    bio: 'Entrepreneur | Bootcamp Graduate | Passionate about business growth and innovation.',
    avatar: { uri: 'https://randomuser.me/api/portraits/women/44.jpg' },
  };

  // Placeholder topics and courses for each level
  const levels: Level[] = ['Discover', 'Scale', 'Grow'];
  const topicsByLevel: Record<Level, Topic[]> = {
    Discover: [
      { name: 'Business Model Canvas', learners: 2 },
      { name: 'Market Research', learners: 4 },
      { name: 'Financial Planning', learners: 6 },
      { name: 'Entrepreneurial Mindset', learners: 8 },
      { name: 'Idea Validation', learners: 10 },
    ],
    Scale: [
      { name: 'Customer Acquisition', learners: 3 },
      { name: 'Pitch Deck Creation', learners: 5 },
      { name: 'Social Media Marketing', learners: 7 },
    ],
    Grow: [
      { name: 'E-commerce Strategy', learners: 2 },
      { name: 'Business Strategy & Growth Hacking', learners: 4 },
      { name: 'Financial Management for Entrepreneurs', learners: 6 },
    ],
  };
  const coursesByLevel: Record<Level, Course[]> = {
    Discover: [
      // Business Model Canvas
      {
        title: 'Business Model Canvas Essentials',
        topic: 'Business Model Canvas',
        instructors: 'John Smith, Sarah Johnson',
        rating: 4.8,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Business Model Canvas Deep Dive',
        topic: 'Business Model Canvas',
        instructors: 'Jane Doe',
        rating: 4.6,
        ratingsCount: 900,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Canvas for Startups',
        topic: 'Business Model Canvas',
        instructors: 'Emily Carter',
        rating: 4.7,
        ratingsCount: 850,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Lean Canvas Workshop',
        topic: 'Business Model Canvas',
        instructors: 'Michael Brown',
        rating: 4.5,
        ratingsCount: 780,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Canvas Masterclass',
        topic: 'Business Model Canvas',
        instructors: 'Olivia Turner',
        rating: 4.9,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Business Model Canvas for Innovators',
        topic: 'Business Model Canvas',
        instructors: 'Samuel Green',
        rating: 4.8,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Canvas Strategy Bootcamp',
        topic: 'Business Model Canvas',
        instructors: 'Sophia Lee',
        rating: 4.7,
        ratingsCount: 1000,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
      // Market Research
      {
        title: 'Market Research Fundamentals',
        topic: 'Market Research',
        instructors: 'Emily Rodriguez',
        rating: 4.7,
        ratingsCount: 950,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Advanced Market Research',
        topic: 'Market Research',
        instructors: 'Chris Lee',
        rating: 4.5,
        ratingsCount: 700,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Market Analysis Bootcamp',
        topic: 'Market Research',
        instructors: 'Sarah Kim',
        rating: 4.8,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Consumer Insights Mastery',
        topic: 'Market Research',
        instructors: 'David Green',
        rating: 4.6,
        ratingsCount: 820,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Market Research for Startups',
        topic: 'Market Research',
        instructors: 'Olivia Turner',
        rating: 4.9,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Competitive Analysis Lab',
        topic: 'Market Research',
        instructors: 'Samuel Green',
        rating: 4.8,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Market Trends Bootcamp',
        topic: 'Market Research',
        instructors: 'Sophia Lee',
        rating: 4.7,
        ratingsCount: 1000,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
      // Financial Planning
      {
        title: 'Financial Planning Basics',
        topic: 'Financial Planning',
        instructors: 'Alex Thompson',
        rating: 4.6,
        ratingsCount: 800,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Personal Finance for Entrepreneurs',
        topic: 'Financial Planning',
        instructors: 'Sarah Johnson',
        rating: 4.8,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Startup Financials 101',
        topic: 'Financial Planning',
        instructors: 'Brian Lee',
        rating: 4.7,
        ratingsCount: 900,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Financial Forecasting Bootcamp',
        topic: 'Financial Planning',
        instructors: 'Jessica White',
        rating: 4.9,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      // Entrepreneurial Mindset
      {
        title: 'Entrepreneurial Mindset Mastery',
        topic: 'Entrepreneurial Mindset',
        instructors: 'Lisa Park',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Mindset for Success',
        topic: 'Entrepreneurial Mindset',
        instructors: 'Chris Lee',
        rating: 4.9,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Growth Mindset Bootcamp',
        topic: 'Entrepreneurial Mindset',
        instructors: 'Anna Smith',
        rating: 4.8,
        ratingsCount: 1000,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Resilient Entrepreneur',
        topic: 'Entrepreneurial Mindset',
        instructors: 'Tom Brown',
        rating: 4.7,
        ratingsCount: 950,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
      },
      // Idea Validation
      {
        title: 'Idea Validation Bootcamp',
        topic: 'Idea Validation',
        instructors: 'Chris Lee',
        rating: 4.9,
        ratingsCount: 1500,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Validating Your Startup Idea',
        topic: 'Idea Validation',
        instructors: 'Jane Doe',
        rating: 4.8,
        ratingsCount: 1000,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Startup Validation Lab',
        topic: 'Idea Validation',
        instructors: 'Emily Carter',
        rating: 4.7,
        ratingsCount: 900,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Idea Testing Workshop',
        topic: 'Idea Validation',
        instructors: 'Michael Brown',
        rating: 4.6,
        ratingsCount: 850,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
    ],
    Scale: [
      {
        title: 'Customer Acquisition Strategies',
        topic: 'Customer Acquisition',
        instructors: 'Alex Thompson',
        rating: 4.6,
        ratingsCount: 800,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Customer Growth Hacking',
        topic: 'Customer Acquisition',
        instructors: 'Sarah Johnson',
        rating: 4.7,
        ratingsCount: 950,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Pitch Deck Creation Masterclass',
        topic: 'Pitch Deck Creation',
        instructors: 'Lisa Park',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Winning Pitch Decks',
        topic: 'Pitch Deck Creation',
        instructors: 'Chris Lee',
        rating: 4.8,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Social Media Marketing Pro',
        topic: 'Social Media Marketing',
        instructors: 'Maria Garcia',
        rating: 4.8,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Advanced Social Media Marketing',
        topic: 'Social Media Marketing',
        instructors: 'Jane Doe',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Customer Acquisition Strategies',
        topic: 'Customer Acquisition',
        instructors: 'Alex Thompson',
        rating: 4.6,
        ratingsCount: 800,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Customer Growth Hacking',
        topic: 'Customer Acquisition',
        instructors: 'Sarah Johnson',
        rating: 4.7,
        ratingsCount: 950,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Pitch Deck Creation Masterclass',
        topic: 'Pitch Deck Creation',
        instructors: 'Lisa Park',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Winning Pitch Decks',
        topic: 'Pitch Deck Creation',
        instructors: 'Chris Lee',
        rating: 4.8,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Social Media Marketing Pro',
        topic: 'Social Media Marketing',
        instructors: 'Maria Garcia',
        rating: 4.8,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Advanced Social Media Marketing',
        topic: 'Social Media Marketing',
        instructors: 'Jane Doe',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
    ],
    Grow: [
      {
        title: 'E-commerce Strategy Advanced',
        topic: 'E-commerce Strategy',
        instructors: 'Chris Lee',
        rating: 4.9,
        ratingsCount: 1500,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'E-commerce Growth Mastery',
        topic: 'E-commerce Strategy',
        instructors: 'Sarah Johnson',
        rating: 4.8,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Business Strategy & Growth Hacking',
        topic: 'Business Strategy & Growth Hacking',
        instructors: 'Maria Garcia',
        rating: 4.8,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Growth Hacking Bootcamp',
        topic: 'Business Strategy & Growth Hacking',
        instructors: 'Alex Thompson',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Financial Management for Entrepreneurs',
        topic: 'Financial Management for Entrepreneurs',
        instructors: 'Emily Rodriguez',
        rating: 4.7,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Entrepreneurial Finance',
        topic: 'Financial Management for Entrepreneurs',
        instructors: 'Lisa Park',
        rating: 4.8,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'E-commerce Strategy Advanced',
        topic: 'E-commerce Strategy',
        instructors: 'Chris Lee',
        rating: 4.9,
        ratingsCount: 1500,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'E-commerce Growth Mastery',
        topic: 'E-commerce Strategy',
        instructors: 'Sarah Johnson',
        rating: 4.8,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Business Strategy & Growth Hacking',
        topic: 'Business Strategy & Growth Hacking',
        instructors: 'Maria Garcia',
        rating: 4.8,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Growth Hacking Bootcamp',
        topic: 'Business Strategy & Growth Hacking',
        instructors: 'Alex Thompson',
        rating: 4.7,
        ratingsCount: 1100,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Financial Management for Entrepreneurs',
        topic: 'Financial Management for Entrepreneurs',
        instructors: 'Emily Rodriguez',
        rating: 4.7,
        ratingsCount: 1200,
        price: 'Free',
        badge: 'Premium',
        image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
      },
      {
        title: 'Entrepreneurial Finance',
        topic: 'Financial Management for Entrepreneurs',
        instructors: 'Lisa Park',
        rating: 4.8,
        ratingsCount: 1300,
        price: 'Free',
        badge: 'Bestseller',
        image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
      },
    ],
  };

  // Add notes for each level
  const levelNotes: Record<Level, string> = {
    Discover: 'Discover: Build your foundation with essential business concepts, tools, and frameworks. Perfect for beginners and those validating new ideas.',
    Scale: 'Scale: Take your business to the next level with advanced strategies, marketing, and growth techniques. Ideal for those ready to expand.',
    Grow: 'Grow: Master high-level business skills, financial management, and innovation to sustain and accelerate your success.',
  };

  // State for selected topic per level
  const [selectedTopics, setSelectedTopics] = useState<Record<Level, number>>({ Discover: 0, Scale: 0, Grow: 0 });
  const [showLogin, setShowLogin] = useState(false);
  const [showSignup, setShowSignup] = useState(false);
  // State to track if each level's course list is scrollable
  const [isScrollable, setIsScrollable] = useState<Record<Level, boolean>>({ Discover: false, Scale: false, Grow: false });
  const [scrollPositions, setScrollPositions] = useState<Record<Level, number>>({ Discover: 0, Scale: 0, Grow: 0 });

  // Refs to store ScrollView widths and refs for each level
  const scrollViewWidths = React.useRef<Record<Level, number>>({ Discover: 0, Scale: 0, Grow: 0 });
  const contentWidths = React.useRef<Record<Level, number>>({ Discover: 0, Scale: 0, Grow: 0 });
  const scrollViewRefs = React.useRef<Record<Level, any>>({ Discover: null, Scale: null, Grow: null });

  // Animation for the arrow
  const arrowAnim = React.useRef(new Animated.Value(0)).current;
  React.useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(arrowAnim, { toValue: 8, duration: 700, useNativeDriver: true, easing: Easing.inOut(Easing.quad) }),
        Animated.timing(arrowAnim, { toValue: 0, duration: 700, useNativeDriver: true, easing: Easing.inOut(Easing.quad) }),
      ])
    ).start();
  }, [arrowAnim]);

  const handleSelectTopic = (level: Level, idx: number) => {
    setSelectedTopics((prev) => ({ ...prev, [level]: idx }));
  };

  // Handler to check if ScrollView is scrollable
  const handleContentSizeChange = (level: Level, contentWidth: number, containerWidth: number) => {
    contentWidths.current[level] = contentWidth;
    setIsScrollable((prev) => ({ ...prev, [level]: contentWidth > containerWidth }));
  };

  const handleScroll = (level: Level, event: NativeSyntheticEvent<NativeScrollEvent>) => {
    setScrollPositions(prev => ({ ...prev, [level]: event.nativeEvent.contentOffset.x }));
  };

  const scrollLeft = (level: Level) => {
    const scrollView = scrollViewRefs.current[level];
    if (scrollView) {
      const currentPosition = scrollPositions[level];
      const newPosition = Math.max(0, currentPosition - 300);
      scrollView.scrollTo({ x: newPosition, animated: true });
    }
  };

  const scrollRight = (level: Level) => {
    const scrollView = scrollViewRefs.current[level];
    if (scrollView) {
      const currentPosition = scrollPositions[level];
      const containerWidth = scrollViewWidths.current[level] || 0;
      const contentWidth = contentWidths.current[level] || 0;
      const maxScroll = contentWidth - containerWidth;
      const newPosition = Math.min(maxScroll, currentPosition + 300);
      scrollView.scrollTo({ x: newPosition, animated: true });
    }
  };

  const FALLBACK_IMAGE = { uri: 'https://via.placeholder.com/280x140?text=No+Image' };

  function CourseCard({ course, fallbackImage }: { course: Course; fallbackImage: { uri: string } }) {
    const [imgError, setImgError] = useState(false);
    const goToDetails = () => router.push({ pathname: '/coursedetails', params: { title: course.title } });
    return (
      <TouchableOpacity style={styles.courseCard} onPress={goToDetails} activeOpacity={0.85}>
        <Image
          source={imgError ? fallbackImage : (course.image && course.image.uri ? course.image : fallbackImage)}
          style={styles.courseImage}
          onError={() => setImgError(true)}
        />
        <View style={styles.courseCardContent}>
          <Text style={styles.courseTitle} numberOfLines={1} ellipsizeMode='tail'>{course.title}</Text>
          <Text style={styles.courseInstructors}>{course.instructors}</Text>
          <View style={styles.courseCardRatingRow}>
            <Text style={styles.courseCardStar}>⭐</Text>
            <Text style={styles.courseCardRating}>{course.rating}</Text>
            <Text style={styles.courseCardRatingCount}>({course.ratingsCount.toLocaleString()})</Text>
          </View>
          <Text style={styles.courseCardPrice}>{course.price}</Text>
          <TouchableOpacity style={styles.enrollButton} onPress={goToDetails} activeOpacity={0.85}>
            <Text style={styles.enrollButtonText}>Enroll Now</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <>
      <LoginModal open={showLogin} onClose={() => setShowLogin(false)} />
      <SignupModal open={showSignup} onClose={() => setShowSignup(false)} />
      <MainNavbar onShowLogin={() => setShowLogin(true)} onShowSignup={() => setShowSignup(true)} />
      <ScrollView style={styles.container} contentContainerStyle={{ alignItems: 'center', paddingBottom: 32 }}>
        {/* User Profile Section */}
        <View style={styles.profileSectionPro}>
          <Image source={user.avatar} style={styles.avatarPro} />
          <View style={styles.profileInfoPro}>
            <Text style={styles.namePro}>{user.name}</Text>
            <Text style={styles.emailPro}>{user.email}</Text>
            <View style={styles.profileDivider} />
            <Text style={styles.bioPro}>{user.bio}</Text>
          </View>
        </View>

        {/* Learning Levels Section */}
        <View style={styles.levelsSection}>
          <Text style={styles.sectionHeading}>Imuka Juniors Learning Levels</Text>
          <View style={styles.headingUnderline} />
          <Text style={styles.levelsDescription}>
            Our Learning Levels are designed to guide you from foundational knowledge to advanced mastery. Each level contains a curated set of courses, each with multiple modules to deepen your understanding. As you progress, you'll complete assignments and quizzes to reinforce your learning and test your skills. Successfully finishing a course earns you a certificate, which you can showcase in your profile. Dive in, complete modules, ace the quizzes, and collect certifications as you grow your expertise!
          </Text>
          {levels.map((level: Level, i: number) => {
            const topics = topicsByLevel[level];
            const selectedTopicIdx = selectedTopics[level];
            const selectedTopic = topics[selectedTopicIdx];
            const courses = coursesByLevel[level].filter(
              (course: Course) => course.topic === selectedTopic.name
            );
            const containerWidth = scrollViewWidths.current[level] || 0;
            const contentWidth = contentWidths.current[level] || 0;
            const scrollPosition = scrollPositions[level];

            const showLeftArrow = isScrollable[level] && scrollPosition > 10;
            const showRightArrow = isScrollable[level] && scrollPosition < contentWidth - containerWidth - 10;
            return (
              <React.Fragment key={level}>
                <View style={styles.levelBlock}>
                  <Text style={styles.levelTitle}>{level}</Text>
                  <Text style={styles.levelNote}>{levelNotes[level]}</Text>
                  {/* Topic Bubbles */}
                  <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.topicsScroll} contentContainerStyle={{ alignItems: 'center', paddingHorizontal: 0 }}>
                    {topics.map((topic: Topic, idx: number) => (
                      <TouchableOpacity
                        key={idx}
                        style={[
                          styles.topicBubble,
                          idx === selectedTopicIdx && styles.topicBubbleSelected,
                        ]}
                        onPress={() => handleSelectTopic(level, idx)}
                      >
                        <Text style={[
                          styles.topicBubbleText,
                          idx === selectedTopicIdx && styles.topicBubbleTextSelected,
                        ]}>{topic.name}</Text>
                        <Text style={[
                          styles.topicBubbleLearners,
                          idx === selectedTopicIdx && styles.topicBubbleLearnersSelected,
                        ]}>{topic.learners}K+ learners</Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                  {/* Course Cards */}
                  <View style={styles.scrollableRowContainer}>
                    {showLeftArrow && (
                      <Animated.View style={[styles.scrollArrowContainer, styles.leftArrowContainer, { transform: [{ translateX: Animated.multiply(arrowAnim, -1) }] }]}>
                        <TouchableOpacity onPress={() => scrollLeft(level)} style={styles.arrowTouchable}>
                          <View style={styles.arrowCircle}>
                            <Text style={[styles.scrollArrow, styles.leftArrowText]} accessibilityLabel="Scroll back">«</Text>
                          </View>
                        </TouchableOpacity>
                      </Animated.View>
                    )}
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      style={styles.levelCoursesRow}
                      contentContainerStyle={{ paddingVertical: 8 }}
                      onContentSizeChange={(cw, ch) => {
                        const containerW = scrollViewWidths.current[level];
                        if (containerW !== undefined) {
                          handleContentSizeChange(level, cw, containerW);
                        }
                      }}
                      onScroll={(e) => handleScroll(level, e)}
                      scrollEventThrottle={16}
                      ref={ref => { scrollViewRefs.current[level] = ref; }}
                      onLayout={event => {
                        const width = event.nativeEvent.layout.width;
                        scrollViewWidths.current[level] = width;
                        const contentW = contentWidths.current[level];
                        if (contentW) {
                          handleContentSizeChange(level, contentW, width);
                        }
                      }}
                    >
                      {courses.map((course: Course, idx: number) => (
                        <CourseCard key={idx} course={course} fallbackImage={FALLBACK_IMAGE} />
                      ))}
                    </ScrollView>
                    {showRightArrow && (
                      <Animated.View style={[styles.scrollArrowContainer, styles.rightArrowContainer, { transform: [{ translateX: arrowAnim }] }]}>
                        <TouchableOpacity onPress={() => scrollRight(level)} style={styles.arrowTouchable}>
                          <View style={styles.arrowCircle}>
                            <Text style={[styles.scrollArrow, styles.rightArrowText]} accessibilityLabel="Scroll for more">»</Text>
                          </View>
                        </TouchableOpacity>
                      </Animated.View>
                    )}
                  </View>
                </View>
                {i < levels.length - 1 && <View style={styles.levelSeparator} />}
              </React.Fragment>
            );
          })}
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    marginTop: 32,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOpacity: 0.07,
    shadowRadius: 8,
    elevation: 2,
    width: '90%',
    maxWidth: 600,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginRight: 24,
    borderWidth: 2,
    borderColor: '#1976d2',
  },
  profileInfo: {
    flex: 1,
  },
  name: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  email: {
    fontSize: 15,
    color: '#1976d2',
    marginBottom: 6,
  },
  bio: {
    fontSize: 14,
    color: '#444',
  },
  profileSectionPro: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    padding: 28,
    marginTop: 32,
    marginBottom: 32,
    shadowColor: '#000',
    shadowOpacity: 0.07,
    shadowRadius: 8,
    elevation: 2,
    width: '90%',
    maxWidth: 600,
  },
  avatarPro: {
    width: 90,
    height: 90,
    borderRadius: 45,
    marginRight: 32,
    borderWidth: 3,
    borderColor: '#1976d2',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOpacity: 0.10,
    shadowRadius: 6,
    elevation: 2,
  },
  profileInfoPro: {
    flex: 1,
    justifyContent: 'center',
  },
  namePro: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1976d2',
    marginBottom: 2,
  },
  emailPro: {
    fontSize: 15,
    color: '#1976d2',
    marginBottom: 10,
    fontWeight: '500',
  },
  profileDivider: {
    width: 40,
    height: 2,
    backgroundColor: '#e5e5e5',
    marginVertical: 8,
    borderRadius: 1,
  },
  bioPro: {
    fontSize: 15,
    color: '#444',
    fontStyle: 'italic',
    lineHeight: 22,
  },
  sectionHeading: {
    fontSize: 28,
    fontWeight: '800',
    color: '#1976d2',
    marginBottom: 16,
    marginTop: 32,
    alignSelf: 'flex-start',
    marginLeft: 8,
    letterSpacing: 0.2,
    textAlign: 'left',
    position: 'relative',
  },
  headingUnderline: {
    width: 64,
    height: 4,
    backgroundColor: '#1976d2',
    borderRadius: 2,
    marginLeft: 8,
    marginBottom: 28,
    marginTop: 2,
  },
  levelsSection: {
    width: '90%',
    maxWidth: 900,
    marginBottom: 24,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  levelsDescription: {
    fontSize: 15,
    color: '#444',
    marginBottom: 18,
    marginTop: 2,
    textAlign: 'left',
    lineHeight: 22,
    letterSpacing: 0.1,
  },
  levelBlock: {
    width: '100%',
    marginBottom: 32,
  },
  levelTitle: {
    fontSize: 23,
    fontWeight: '700',
    color: '#1976d2',
    marginBottom: 18,
    marginTop: 10,
    marginLeft: 8,
    letterSpacing: 0.12,
  },
  levelNote: {
    fontSize: 14,
    color: '#555',
    marginLeft: 8,
    marginBottom: 12,
    marginTop: -8,
    fontStyle: 'italic',
    lineHeight: 20,
  },
  topicsScroll: {
    marginBottom: 18,
    width: '100%',
    alignSelf: 'center',
    paddingLeft: 0,
  },
  topicBubble: {
    backgroundColor: '#f3f4f6',
    borderRadius: 0,
    paddingHorizontal: 20,
    paddingVertical: 12,
    marginRight: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e5e5',
  },
  topicBubbleSelected: {
    backgroundColor: '#111827',
    borderColor: '#111827',
  },
  topicBubbleText: {
    color: '#111827',
    fontWeight: 'bold',
    fontSize: 13,
    textAlign: 'center',
  },
  topicBubbleTextSelected: {
    color: '#fff',
  },
  topicBubbleLearners: {
    color: '#6b7280',
    fontSize: 10,
    marginTop: 2,
    textAlign: 'center',
  },
  topicBubbleLearnersSelected: {
    color: '#fff',
  },
  levelCoursesRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  scrollableRowContainer: {
    position: 'relative',
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  scrollArrowContainer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    zIndex: 2,
  },
  leftArrowContainer: {
    left: 0,
  },
  rightArrowContainer: {
    right: 0,
  },
  arrowTouchable: {
    padding: 4,
  },
  arrowCircle: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 5,
    borderWidth: 2,
    borderColor: '#1976d2',
  },
  scrollArrow: {
    fontSize: 28,
    color: '#1976d2',
    fontWeight: 'bold',
  },
  leftArrowText: {
    marginLeft: -2,
  },
  rightArrowText: {
    marginRight: -2,
  },
  courseCard: {
    width: 280,
    backgroundColor: '#fff',
    borderRadius: 0,
    marginRight: 24,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    overflow: 'hidden',
  },
  courseImage: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  courseCardContent: {
    padding: 16,
  },
  courseTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 6,
    lineHeight: 22,
    letterSpacing: 0.1,
    textAlign: 'left',
  },
  courseInstructors: {
    fontSize: 13,
    color: '#6b7280',
    marginBottom: 7,
    fontWeight: '400',
    letterSpacing: 0.05,
    textAlign: 'left',
  },
  courseCardRatingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 7,
  },
  courseCardStar: {
    fontSize: 14,
    color: '#FFD700',
    marginRight: 4,
  },
  courseCardRating: {
    fontSize: 13,
    color: '#111827',
    fontWeight: '500',
    marginRight: 4,
  },
  courseCardRatingCount: {
    fontSize: 12,
    color: '#9ca3af',
  },
  courseCardPrice: {
    fontSize: 15,
    fontWeight: '600',
    color: '#1976d2',
    marginBottom: 8,
    letterSpacing: 0.05,
  },
  courseCardBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
    marginTop: 4,
  },
  badgeBestseller: {
    backgroundColor: '#1de9b6',
  },
  badgePremium: {
    backgroundColor: '#1976d2',
  },
  courseCardBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
  enrollButton: {
    marginTop: 8,
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderRadius: 0,
    borderWidth: 1.5,
    borderColor: '#1976d2',
    paddingHorizontal: 16,
    paddingVertical: 7,
    shadowColor: 'transparent',
  },
  enrollButtonText: {
    color: '#1976d2',
    fontWeight: '600',
    fontSize: 15,
    letterSpacing: 0.1,
  },
  levelSeparator: {
    width: '100%',
    height: 1,
    backgroundColor: '#e5e5e5',
    marginVertical: 18,
  },
}); 