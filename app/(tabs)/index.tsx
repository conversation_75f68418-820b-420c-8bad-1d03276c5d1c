import LoginModal from '@/components/LoginModal';
import MainNavbar from '@/components/MainNavbar';
import SignupModal from '@/components/SignupModal';
import React, { useEffect, useState } from 'react';
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View, useWindowDimensions } from 'react-native';

// Mock data for boot camps
const bootCamps = [
  {
    title: 'Startup Launch Boot Camp',
    image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    bgColor: '#E3F2FD',
    icon: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
    rating: 4.8,
    studentsCount: '120',
    duration: '12 weeks',
    startDate: 'March 15, 2024',
    description: 'Launch your startup with hands-on mentorship and real-world projects.'
  },
  {
    title: 'Digital Marketing Mastery',
    image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    bgColor: '#E8F4FD',
    icon: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    rating: 4.7,
    studentsCount: '85',
    duration: '10 weeks',
    startDate: 'April 1, 2024',
    description: 'Master digital marketing strategies to grow your business online.'
  },
  {
    title: 'Business Strategy & Growth',
    image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    bgColor: '#F0F8FF',
    icon: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
    rating: 4.6,
    studentsCount: '95',
    duration: '8 weeks',
    startDate: 'March 25, 2024',
    description: 'Develop advanced strategies for scaling and sustaining business growth.'
  },
];

const learningLevels = [
  'Discover',
  'Scale', 
  'Grow',
];

// Mock data for learning levels
const levelTabsData = [
  { name: 'Discover' },
  { name: 'Scale' },
  { name: 'Grow' },
];

// Mock courses per level
const levelCourses = {
  Discover: [
    { name: 'Business Model Canvas', courseTime: '2hr 30min' },
    { name: 'Market Research', courseTime: '4hr 15min' },
    { name: 'Financial Planning', courseTime: '6hr 45min' },
  ],
  Scale: [
    { name: 'Customer Acquisition', courseTime: '3hr 20min' },
    { name: 'Pitch Deck Creation', courseTime: '5hr 10min' },
    { name: 'Social Media Marketing', courseTime: '3hr 45min' },
  ],
  Grow: [
    { name: 'E-commerce Strategy', courseTime: '2hr 15min' },
    { name: 'Business Strategy & Growth Hacking', courseTime: '4hr 30min' },
    { name: 'Financial Management for Entrepreneurs', courseTime: '3hr 50min' },
  ],
};

const popularCourses = [
  { name: 'Business Model Canvas', courseTime: '2hr 30min', isSelected: true },
  { name: 'Market Research', courseTime: '4hr 15min', isSelected: false },
  { name: 'Financial Planning', courseTime: '6hr 45min', isSelected: false },
  { name: 'Customer Acquisition', courseTime: '3hr 20min', isSelected: false },
  { name: 'Pitch Deck Creation', courseTime: '5hr 10min', isSelected: false },
  { name: 'Social Media Marketing', courseTime: '3hr 45min', isSelected: false },
  { name: 'E-commerce Strategy', courseTime: '2hr 15min', isSelected: false },
];

// Mock featured courses per level
const featuredCoursesByLevel: { [key: string]: { title: string; instructors: string; rating: number; ratingsCount: string; price: string; badge: string; image: { uri: string } }[] } = {
  Discover: [
    {
      title: 'Business Model Canvas Essentials',
      instructors: 'John Smith, Sarah Johnson',
      rating: 4.8,
      ratingsCount: '1,200',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Market Research Fundamentals',
      instructors: 'Emily Rodriguez',
      rating: 4.7,
      ratingsCount: '950',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Financial Planning Basics',
      instructors: 'Alex Thompson',
      rating: 4.6,
      ratingsCount: '800',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Entrepreneurial Mindset Mastery',
      instructors: 'Lisa Park',
      rating: 4.7,
      ratingsCount: '1,100',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Idea Validation Bootcamp',
      instructors: 'Chris Lee',
      rating: 4.9,
      ratingsCount: '1,500',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
    },
  ],
  Scale: [
    {
      title: 'Customer Acquisition Strategies',
      instructors: 'Alex Thompson',
      rating: 4.6,
      ratingsCount: '800',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Pitch Deck Creation Masterclass',
      instructors: 'Lisa Park',
      rating: 4.7,
      ratingsCount: '1,100',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Social Media Marketing Pro',
      instructors: 'Maria Garcia',
      rating: 4.8,
      ratingsCount: '1,300',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Growth Hacking Bootcamp',
      instructors: 'David Wilson',
      rating: 4.7,
      ratingsCount: '1,050',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Brand Building Essentials',
      instructors: 'Sarah Johnson',
      rating: 4.5,
      ratingsCount: '900',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    },
  ],
  Grow: [
    {
      title: 'E-commerce Strategy Advanced',
      instructors: 'Chris Lee',
      rating: 4.9,
      ratingsCount: '1,500',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Business Strategy & Growth Hacking',
      instructors: 'Maria Garcia',
      rating: 4.8,
      ratingsCount: '1,300',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Financial Management for Entrepreneurs',
      instructors: 'Emily Rodriguez',
      rating: 4.7,
      ratingsCount: '1,200',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Leadership for Founders',
      instructors: 'Alex Thompson',
      rating: 4.6,
      ratingsCount: '1,100',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Exit Strategies Bootcamp',
      instructors: 'Lisa Park',
      rating: 4.5,
      ratingsCount: '950',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
    },
  ],
};

// Mock data for slider
const sliderData = [
  {
    title: 'Build Your Business Empire',
    subtitle: 'Master entrepreneurial skills and launch successful ventures. Start your journey with Imuka Juniors.',
    image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Join Our Boot Camps',
    subtitle: 'Intensive programs designed for entrepreneurs. Learn from successful business leaders.',
    image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Progress Through Levels',
    subtitle: 'Discover → Scale → Grow. Structured path for your entrepreneurial success.',
    image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Network with Innovators',
    subtitle: 'Connect with like-minded entrepreneurs and mentors to grow your business.',
    image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Access Real-World Resources',
    subtitle: 'Get hands-on with tools, templates, and case studies for your startup.',
    image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
  },
];

// Learning levels data with title, description, image, and courses
const learningLevelsData: { [key: string]: { title: string; description: string; image: { uri: string }; courses: { name: string; courseTime: string }[] } } = {
  Discover: {
    title: 'Discover',
    description: 'Start your entrepreneurial journey by learning the fundamentals of business, market research, and planning.',
    image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    courses: [
      { name: 'Business Model Canvas', courseTime: '2hr 30min' },
      { name: 'Market Research', courseTime: '4hr 15min' },
      { name: 'Financial Planning', courseTime: '6hr 45min' },
      { name: 'Entrepreneurial Mindset', courseTime: '3hr 10min' },
      { name: 'Idea Validation', courseTime: '2hr 50min' },
    ],
  },
  Scale: {
    title: 'Scale',
    description: 'Take your business to the next level with customer acquisition, marketing, and pitching skills.',
    image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    courses: [
      { name: 'Customer Acquisition', courseTime: '3hr 20min' },
      { name: 'Pitch Deck Creation', courseTime: '5hr 10min' },
      { name: 'Social Media Marketing', courseTime: '3hr 45min' },
      { name: 'Growth Hacking', courseTime: '4hr 00min' },
      { name: 'Brand Building', courseTime: '2hr 40min' },
    ],
  },
  Grow: {
    title: 'Grow',
    description: 'Master advanced strategies for scaling, e-commerce, and financial management for entrepreneurs.',
    image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
    courses: [
      { name: 'E-commerce Strategy', courseTime: '2hr 15min' },
      { name: 'Business Strategy & Growth Hacking', courseTime: '4hr 30min' },
      { name: 'Financial Management for Entrepreneurs', courseTime: '3hr 50min' },
      { name: 'Leadership for Founders', courseTime: '3hr 20min' },
      { name: 'Exit Strategies', courseTime: '2hr 55min' },
    ],
  },
};

// Utility for responsive font size
function getResponsiveFontSize({ mobile, tablet, desktop }: { mobile: number; tablet: number; desktop: number }, isMobile: boolean, isTablet: boolean, isDesktop: boolean) {
  if (isMobile) return mobile;
  if (isTablet) return tablet;
  return desktop;
}

export default function HomeScreen() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedLevel, setSelectedLevel] = useState('Discover');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { width: windowWidth } = useWindowDimensions();
  const isMobile = windowWidth < 700;
  const isTablet = windowWidth >= 700 && windowWidth < 1000;
  const isDesktop = windowWidth >= 1000;
  const [showLogin, setShowLogin] = useState(false);
  const [showSignup, setShowSignup] = useState(false);

  // Auto-scroll functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderData.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % sliderData.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + sliderData.length) % sliderData.length);
  };

  // Typography breakpoints
  const getTitleStyle = () => {
    if (isMobile) return { fontSize: 24, lineHeight: 30 };
    if (isTablet) return { fontSize: 32, lineHeight: 38 };
    return { fontSize: 36, lineHeight: 44 };
  };
  const getSubtitleStyle = () => {
    if (isMobile) return { fontSize: 16, lineHeight: 22 };
    if (isTablet) return { fontSize: 20, lineHeight: 28 };
    return { fontSize: 22, lineHeight: 30 };
  };
  const getBodyStyle = () => {
    if (isMobile) return { fontSize: 16, lineHeight: 22 };
    if (isTablet) return { fontSize: 17, lineHeight: 24 };
    return { fontSize: 18, lineHeight: 26 };
  };

  // Responsive spacing utility
  const getSectionSpacing = () => {
    if (isMobile) return { marginTop: 18, marginBottom: 18, paddingVertical: 16 };
    if (isTablet) return { marginTop: 32, marginBottom: 32, paddingVertical: 28 };
    return { marginTop: 48, marginBottom: 48, paddingVertical: 40 };
  };
  const getBannerStyle = () => {
    if (isMobile) return { paddingVertical: 14 };
    if (isTablet) return { paddingVertical: 18 };
    return { paddingVertical: 22 };
  };
  const getBannerTextStyle = () => {
    if (isMobile) return { fontSize: 17 };
    if (isTablet) return { fontSize: 19 };
    return { fontSize: 22 };
  };
  const getBannerButtonStyle = () => {
    if (isMobile) return { paddingVertical: 4, paddingHorizontal: 10, fontSize: 15 };
    if (isTablet) return { paddingVertical: 6, paddingHorizontal: 14, fontSize: 16 };
    return { paddingVertical: 8, paddingHorizontal: 18, fontSize: 17 };
  };

  // Responsive element spacing utility
  const getElementSpacing = (type = 'default') => {
    if (type === 'title') {
      if (isMobile) return { marginBottom: 8 };
      if (isTablet) return { marginBottom: 16 };
      return { marginBottom: 24 };
    }
    if (type === 'subtitle') {
      if (isMobile) return { marginBottom: 12 };
      if (isTablet) return { marginBottom: 18 };
      return { marginBottom: 24 };
    }
    // default
    if (isMobile) return { marginBottom: 8 };
    if (isTablet) return { marginBottom: 12 };
    return { marginBottom: 16 };
  };

  return (
    <>
      <LoginModal open={showLogin} onClose={() => setShowLogin(false)} />
      <SignupModal open={showSignup} onClose={() => setShowSignup(false)} />
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.contentContainer}>
        {/* Top Banner */}
        <View style={[
          styles.topBanner,
          getBannerStyle(),
          {
            shadowColor: '#000',
            shadowOpacity: 0.08,
            shadowRadius: 6,
            elevation: 2,
            borderBottomWidth: 1,
            borderBottomColor: '#1565c0',
            marginTop: 0,
            flexDirection: 'row',
            justifyContent: 'center',
            alignItems: 'center',
            flexWrap: 'wrap',
          },
        ]}>
          <Text style={[styles.topBannerText, getBannerTextStyle(), { textAlign: 'center', marginBottom: 0, alignSelf: 'center' }]}> 
            Ready to start your entrepreneurial journey?
          </Text>
          <TouchableOpacity style={{ backgroundColor: '#fff', borderRadius: 16, ...getBannerButtonStyle(), marginLeft: 8, alignSelf: 'center' }}>
            <Text style={[styles.topBannerButtonText, { color: '#1976d2', fontSize: getBannerButtonStyle().fontSize }]}> 
              Join Imuka Juniors Boot Camps
            </Text>
          </TouchableOpacity>
        </View>

        {/* Header / Navbar */}
        <MainNavbar onShowLogin={() => setShowLogin(true)} onShowSignup={() => setShowSignup(true)} />

        {/* Hero Section */}
        <View style={[
          styles.heroSliderWrap,
          {
            flexDirection: isMobile ? 'column' : 'row',
            maxWidth: isMobile ? '100%' : 1200,
            width: '100%',
            alignSelf: 'center',
            minHeight: isMobile ? 260 : 360,
            ...getSectionSpacing(),
          },
        ]}> 
          {/* Full background image */}
          <Image source={sliderData[currentSlide].image} style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            resizeMode: 'cover',
            zIndex: 0,
          }} />
          {/* Darker overlay for readability */}
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(17,24,39,0.35)',
            zIndex: 1,
          }} />
          <TouchableOpacity style={styles.heroArrowBtn} onPress={prevSlide}>
            <Text style={styles.heroArrowText}>{'<'}</Text>
          </TouchableOpacity>
          <View style={[styles.heroSliderContent, {
            zIndex: 2,
            justifyContent: isMobile ? 'flex-start' : 'center',
            alignItems: isMobile ? 'center' : 'flex-start',
            minHeight: isMobile ? 180 : 240,
            paddingHorizontal: isMobile ? 12 : 0,
            width: '100%',
          }]}> 
            {/* Card overlay for text */}
            <View style={{
              backgroundColor: 'rgba(255,255,255,0.92)',
              borderRadius: 18,
              paddingVertical: isMobile ? 18 : 32,
              paddingHorizontal: isMobile ? 16 : 36,
              maxWidth: isMobile ? '100%' : 420,
              marginLeft: isMobile ? 0 : 24,
              shadowColor: '#000',
              shadowOpacity: 0.10,
              shadowRadius: 16,
              elevation: 4,
            }}>
              <Text style={[styles.heroSliderTitle, { fontSize: 24, fontWeight: 'bold', color: '#111827', marginBottom: 16, lineHeight: 32, letterSpacing: -0.5, textAlign: isMobile ? 'left' : 'center' }]}>{sliderData[currentSlide].title}</Text>
              <Text style={[styles.heroSliderSubtitle, { fontSize: 20, color: '#6b7280', lineHeight: 28, fontWeight: '400', letterSpacing: 0.2, textAlign: isMobile ? 'left' : 'center' }]}>{sliderData[currentSlide].subtitle}</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.heroArrowBtn} onPress={nextSlide}>
            <Text style={styles.heroArrowText}>{'>'}</Text>
          </TouchableOpacity>
        </View>

        {/* Slide Indicators */}
        <View style={styles.slideIndicators}>
          {sliderData.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.slideIndicator, index === currentSlide && styles.slideIndicatorActive]}
              onPress={() => setCurrentSlide(index)}
            />
          ))}
        </View>

        {/* About Us Section */}
        <View style={[styles.aboutSection, getSectionSpacing()]}>
          <View style={styles.aboutContainer}>
            <Text style={[styles.aboutTitle, getTitleStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>About Imuka Juniors</Text>
            <Text style={[styles.aboutMission, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>
              Imuka Juniors is dedicated to empowering entrepreneurs with the skills and knowledge needed to build successful businesses. We provide comprehensive boot camps and structured learning programs that combine practical experience with proven business strategies to accelerate your entrepreneurial journey.
            </Text>
            
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>500+</Text>
                <Text style={[styles.statLabel, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Entrepreneurs</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>15+</Text>
                <Text style={[styles.statLabel, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Boot Camps</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>95%</Text>
                <Text style={[styles.statLabel, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Success Rate</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>50+</Text>
                <Text style={[styles.statLabel, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Business Mentors</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Udemy-style Learning Levels Section */}
        <View style={{ backgroundColor: '#fff', borderRadius: 24, width: '100%', maxWidth: isMobile ? '100%' : 1200, alignSelf: 'center', paddingHorizontal: isMobile ? 8 : 0, ...getSectionSpacing() }}>
          {/* Section Title & Subtitle */}
          <Text
            style={[
              { fontWeight: 'bold', color: '#111827', marginTop: isMobile ? 18 : 32, letterSpacing: -0.5, textAlign: isMobile ? 'left' : 'center' },
              getTitleStyle(),
              getElementSpacing('title'),
            ]}
          >
            All the skills you need in one place
          </Text>
          <Text
            style={[
              { color: '#6b7280', maxWidth: 600, alignSelf: 'center', textAlign: isMobile ? 'left' : 'center' },
              getSubtitleStyle(),
              getElementSpacing('subtitle'),
            ]}
          >
            From critical skills to technical topics, Imuka Juniors supports your professional development.
          </Text>
          {/* Horizontal Level Tabs */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: 12 }} contentContainerStyle={{ alignItems: 'center', paddingHorizontal: 16 }}>
            {levelTabsData.map((tab, idx) => (
              <TouchableOpacity
                key={idx}
                style={{
                  paddingHorizontal: 18,
                  paddingVertical: 10,
                  marginRight: 8,
                  borderBottomWidth: selectedLevel === tab.name ? 3 : 0,
                  borderBottomColor: selectedLevel === tab.name ? '#1976d2' : 'transparent',
                }}
                onPress={() => setSelectedLevel(tab.name)}
              >
                <Text style={{
                  fontSize: 14,
                  fontWeight: selectedLevel === tab.name ? 'bold' : '600',
                  color: selectedLevel === tab.name ? '#111827' : '#6b7280',
                  letterSpacing: 0.2,
                }}>{tab.name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          {/* Horizontal Topic Bubbles */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: isMobile ? 12 : 24 }} contentContainerStyle={{ alignItems: 'center', paddingHorizontal: 16 }}>
            {learningLevelsData[selectedLevel].courses.map((course: any, idx: number) => (
              <View key={idx} style={{
                backgroundColor: idx === 0 ? '#111827' : '#f3f4f6',
                borderRadius: 24,
                paddingHorizontal: 20,
                paddingVertical: 12,
                marginRight: 12,
                alignItems: 'center',
                borderWidth: idx === 0 ? 0 : 1,
                borderColor: '#e5e5e5',
              }}>
                <Text style={{ color: idx === 0 ? '#fff' : '#111827', fontWeight: 'bold', fontSize: 13, textAlign: isMobile ? 'left' : 'center' }}>{course.name}</Text>
                <Text style={{ color: idx === 0 ? '#fff' : '#6b7280', fontSize: 10, marginTop: 2, textAlign: isMobile ? 'left' : 'center' }}>{(idx + 1) * 2}K+ learners</Text>
              </View>
            ))}
          </ScrollView>
          {/* Horizontal Scrollable Course Cards */}
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ marginBottom: isMobile ? 12 : 24, width: '100%', alignSelf: 'center', paddingLeft: 0 }}>
            {featuredCoursesByLevel[selectedLevel].map((course: any, idx: number) => (
              <View key={idx} style={{
                width: isMobile ? 260 : 320,
                backgroundColor: '#fff',
                borderRadius: 12,
                marginRight: 20,
                shadowColor: '#000',
                shadowOpacity: 0.08,
                shadowRadius: 8,
                elevation: 3,
                borderWidth: 1,
                borderColor: '#e5e5e5',
                overflow: 'hidden',
              }}>
                <Image source={course.image} style={{ width: '100%', height: isMobile ? 120 : 160, borderTopLeftRadius: 12, borderTopRightRadius: 12 }} />
                <View style={{ padding: 16 }}>
                  <Text style={{ fontSize: 13, fontWeight: 'bold', color: '#111827', marginBottom: 6, lineHeight: 20, textAlign: isMobile ? 'left' : 'center' }}>{course.title}</Text>
                  <Text style={{ fontSize: 11, color: '#6b7280', marginBottom: 8, textAlign: isMobile ? 'left' : 'center' }}>{course.instructors}</Text>
                  <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 8 }}>
                    <Text style={{ fontSize: 11, color: '#6b7280', marginRight: 6, textAlign: isMobile ? 'left' : 'center' }}>⭐ {course.rating}</Text>
                    <Text style={{ fontSize: 11, color: '#9ca3af', textAlign: isMobile ? 'left' : 'center' }}>({course.ratingsCount})</Text>
                  </View>
                  <Text style={{ fontSize: 15, fontWeight: 'bold', color: '#111827', marginBottom: 8, textAlign: isMobile ? 'left' : 'center' }}>{course.price}</Text>
                  <View style={{ alignSelf: 'flex-start', paddingHorizontal: 10, paddingVertical: 6, borderRadius: 6, backgroundColor: course.badge === 'Bestseller' ? '#1de9b6' : '#1976d2' }}>
                    <Text style={{ fontSize: 12, color: '#fff', fontWeight: 'bold' }}>{course.badge}</Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
          <TouchableOpacity style={[styles.showAllButton, { marginTop: isMobile ? 8 : 16, paddingHorizontal: isMobile ? 16 : 24, paddingVertical: isMobile ? 8 : 12 }]}>
            <Text style={[styles.showAllButtonText, { fontSize: 13 }]}>Show all Entrepreneurship courses</Text>
          </TouchableOpacity>
        </View>

        {/* Career Accelerators */}
        <View style={[styles.section, getSectionSpacing()]}>
          <Text style={[styles.careerSectionTitle, getTitleStyle(), getElementSpacing('title'), { textAlign: isMobile ? 'left' : 'center' }]}>Ready to join our boot camps?</Text>
          <Text style={[styles.careerSectionSubtitle, getSubtitleStyle(), getElementSpacing('subtitle'), { textAlign: isMobile ? 'left' : 'center' }]}>Intensive programs designed for entrepreneurs. Learn from successful business leaders.</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ marginBottom: isMobile ? 12 : 24, width: '100%', alignSelf: 'center', paddingLeft: 0 }}
            contentContainerStyle={{ alignItems: 'flex-start', paddingHorizontal: 8 }}
          >
            {bootCamps.map((c, idx) => (
              <View key={idx} style={[styles.careerCardExact, { width: isMobile ? 260 : 320 }]}> 
                <Image source={c.image} style={{ width: '100%', height: isMobile ? 120 : 180, borderTopLeftRadius: 16, borderTopRightRadius: 16, marginBottom: 0 }} />
                <View style={{ padding: 16, width: '100%' }}>
                  <Text style={[styles.sectionSubtitle, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>{c.title}</Text>
                  <Text style={[styles.sectionSubtitle, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>{c.description}</Text>
                  <Text style={[styles.sectionSubtitle, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Start: {c.startDate}</Text>
                  <Text style={[styles.sectionSubtitle, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Duration: {c.duration}</Text>
                </View>
              </View>
            ))}
          </ScrollView>
          <TouchableOpacity style={styles.careerExactButton}><Text style={styles.careerExactButtonText}>View All Boot Camps</Text></TouchableOpacity>
        </View>

        {/* Testimonials Section */}
        <View style={[styles.testimonialsSection, getSectionSpacing()]}>
          <View style={styles.testimonialsContainer}>
            <Text style={[styles.testimonialsTitle, getTitleStyle(), getElementSpacing('title'), { textAlign: isMobile ? 'left' : 'center' }]}>What Our Entrepreneurs Say</Text>
            <Text style={[styles.testimonialsSubtitle, getSubtitleStyle(), getElementSpacing('subtitle'), { textAlign: isMobile ? 'left' : 'center' }]}>
              Don't just take our word for it. Here's what our entrepreneurs have to say about their business journey with Imuka Juniors.
            </Text>
            
            <View style={styles.testimonialsGrid}>
              <View style={styles.testimonialCard}>
                <View style={styles.testimonialStars}>
                  <Text style={styles.starIcon}>⭐⭐⭐⭐⭐</Text>
                </View>
                <Text style={[styles.testimonialText, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>
                  "Imuka Juniors boot camp completely transformed my business approach. The structured learning and practical strategies helped me launch my startup within 6 months!"
                </Text>
                <View style={styles.testimonialAuthor}>
                  <Image source={{ uri: 'https://images.unsplash.com/photo-1511367461989-f85a21fda167?auto=format&fit=crop&w=400&q=80' }} style={styles.authorAvatar} />
                  <View style={styles.authorInfo}>
                    <Text style={[styles.authorName, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Sarah Johnson</Text>
                    <Text style={[styles.authorRole, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Founder, TechStartup</Text>
                  </View>
                </View>
              </View>

              <View style={styles.testimonialCard}>
                <View style={styles.testimonialStars}>
                  <Text style={styles.starIcon}>⭐⭐⭐⭐⭐</Text>
                </View>
                <Text style={[styles.testimonialText, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>
                  "The Discover → Scale → Grow learning path is brilliant. I started with just an idea and now I'm running a successful e-commerce business. The mentors are amazing!"
                </Text>
                <View style={styles.testimonialAuthor}>
                  <Image source={{ uri: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=crop&w=400&q=80' }} style={styles.authorAvatar} />
                  <View style={styles.authorInfo}>
                    <Text style={[styles.authorName, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Michael Chen</Text>
                    <Text style={[styles.authorRole, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>CEO, E-commerce Empire</Text>
                  </View>
                </View>
              </View>

              <View style={styles.testimonialCard}>
                <View style={styles.testimonialStars}>
                  <Text style={styles.starIcon}>⭐⭐⭐⭐⭐</Text>
                </View>
                <Text style={[styles.testimonialText, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>
                  "The practical business strategies and real-world case studies made all the difference. I went from having just an idea to running a profitable consulting business. Highly recommend!"
                </Text>
                <View style={styles.testimonialAuthor}>
                  <Image source={{ uri: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=400&q=80' }} style={styles.authorAvatar} />
                  <View style={styles.authorInfo}>
                    <Text style={[styles.authorName, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Emily Rodriguez</Text>
                    <Text style={[styles.authorRole, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Founder, DigitalAgency</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View style={[styles.footer, getSectionSpacing()]}>
          <View style={styles.footerContainer}>
            <View style={styles.footerSection}>
              <Text style={[styles.footerTitle, getTitleStyle(), getElementSpacing('title'), { textAlign: isMobile ? 'left' : 'center' }]}>Imuka Juniors</Text>
              <Text style={[styles.footerDescription, getSubtitleStyle(), getElementSpacing('subtitle'), { textAlign: isMobile ? 'left' : 'center' }]}>
                Empowering entrepreneurs with comprehensive boot camps and structured learning programs for business success.
              </Text>
            </View>
            
            <View style={styles.footerSection}>
              <Text style={[styles.footerSectionTitle, getTitleStyle(), getElementSpacing('title'), { textAlign: isMobile ? 'left' : 'center' }]}>Company</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>About Us</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Our Services</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Projects</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Contact</Text>
            </View>
            
            <View style={styles.footerSection}>
              <Text style={[styles.footerSectionTitle, getTitleStyle(), getElementSpacing('title'), { textAlign: isMobile ? 'left' : 'center' }]}>Learning Levels</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Discover</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Scale</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Grow</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>Boot Camps</Text>
            </View>
            
            <View style={styles.footerSection}>
              <Text style={[styles.footerSectionTitle, getTitleStyle(), getElementSpacing('title'), { textAlign: isMobile ? 'left' : 'center' }]}>Contact</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}><EMAIL></Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>+****************</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>123 Tech Street, Suite 200</Text>
              <Text style={[styles.footerLink, getBodyStyle(), { textAlign: isMobile ? 'left' : 'center' }]}>San Francisco, CA 94105</Text>
            </View>
          </View>
          
          <View style={{ width: '100%', maxWidth: isMobile ? '100%' : 1200, alignSelf: 'center', paddingHorizontal: isMobile ? 8 : 0, paddingVertical: isMobile ? 16 : 32, backgroundColor: '#f8f9fa', borderTopWidth: 1, borderTopColor: '#e5e5e5', flexDirection: isMobile ? 'column' : 'row', alignItems: isMobile ? 'flex-start' : 'center', justifyContent: 'space-between', gap: isMobile ? 12 : 0 }}>
            <Text style={[styles.footerCopyright, isMobile && { fontSize: 12, lineHeight: 16 }]}>© 2024 Imuka Juniors. All rights reserved.</Text>
            <View style={{ flexDirection: 'row', gap: 16 }}>
              <Text style={{ fontSize: 13, color: '#1976d2' }}>LinkedIn</Text>
              <Text style={{ fontSize: 13, color: '#1976d2' }}>Twitter</Text>
              <Text style={{ fontSize: 13, color: '#1976d2' }}>Facebook</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  topBannerButtonText: {
    fontWeight: 'bold',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    alignItems: 'center',
  },
  topBanner: {
    backgroundColor: '#1976d2',
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  topBannerText: {
    color: '#fff',
    fontWeight: 'bold',
    letterSpacing: 0.2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  logo: {
    width: 36,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginRight: 20,
    letterSpacing: -0.5,
  },
  headerLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 20,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  searchInput: {
    flex: 1,
    height: 42,
    backgroundColor: '#f8f9fa',
    borderRadius: 21,
    paddingHorizontal: 18,
    fontSize: 16,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    fontWeight: '400',
  },
  headerNavLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 16,
    fontWeight: '600',
    letterSpacing: 0.1,
  },
  loginButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#1976d2',
    marginRight: 10,
  },
  loginText: {
    color: '#1976d2',
    fontWeight: '600',
    fontSize: 16,
  },
  signupButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#1976d2',
    marginRight: 12,
  },
  signupText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  heroSliderWrap: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
    backgroundColor: '#f8f9fa',
    paddingVertical: 40,
    marginTop: 24,
    marginBottom: 40,
  },
  heroArrowBtn: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    marginHorizontal: 12,
    zIndex: 2,
  },
  heroArrowText: {
    fontSize: 24,
    color: '#111827',
    fontWeight: 'bold',
  },
  heroSliderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    width: '100%',
    maxWidth: 900,
    borderRadius: 0,
    justifyContent: 'space-between',
    overflow: 'visible',
  },
  heroSliderCard: {
    backgroundColor: 'transparent',
    borderRadius: 0,
    padding: 32,
    marginRight: 40,
    minWidth: 320,
    maxWidth: 380,
  },
  heroSliderTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    lineHeight: 32,
    letterSpacing: -0.5,
  },
  heroSliderSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  heroSliderImageWrap: {
    position: 'relative',
    width: 360,
    height: 240,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  heroSliderAccent: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 240,
    height: 120,
    backgroundColor: '#1de9b6',
    borderTopLeftRadius: 32,
    borderBottomRightRadius: 20,
    zIndex: 1,
  },
  heroSliderImage: {
    width: 240,
    height: 240,
    borderRadius: 20,
    resizeMode: 'contain',
    zIndex: 2,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 32,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#222',
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#444',
    marginBottom: 16,
  },
  careerCardsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 12,
    gap: 18,
  },
  careerCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginRight: 18,
    alignItems: 'center',
    width: 220,
    shadowColor: '#000',
    shadowOpacity: 0.07,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#eee',
  },
  careerCardImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginBottom: 10,
  },
  careerCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 8,
    textAlign: 'center',
  },
  careerCardStatsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
  careerCardStat: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  careerButton: {
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderColor: '#5624d0',
    borderWidth: 1,
    borderRadius: 4,
    paddingHorizontal: 14,
    paddingVertical: 8,
    marginTop: 6,
  },
  careerButtonText: {
    color: '#5624d0',
    fontWeight: 'bold',
    fontSize: 16,
  },
  categoriesScroll: {
    marginTop: 10,
    marginBottom: 8,
  },
  categoryBadge: {
    backgroundColor: '#f7f7f7',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  categoryBadgeText: {
    color: '#5624d0',
    fontWeight: 'bold',
    fontSize: 16,
  },
  careerSectionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 38,
    letterSpacing: -0.8,
  },
  careerSectionSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  careerCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 24,
    marginBottom: 16,
  },
  careerCardExact: {
    backgroundColor: '#fff',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    width: 320,
    marginHorizontal: 8,
    paddingBottom: 20,
    alignItems: 'flex-start',
  },
  careerCardImageWrap: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    padding: 20,
    position: 'relative',
  },
  careerCardIcon: {
    width: 56,
    height: 56,
    borderRadius: 12,
    marginRight: 12,
  },
  careerCardAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: '#fff',
    position: 'absolute',
    left: 80,
    bottom: 12,
  },
  careerCardExactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginLeft: 20,
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  careerCardBadgesRow: {
    flexDirection: 'row',
    marginTop: 12,
    marginLeft: 20,
    gap: 8,
  },
  careerBadge: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#1976d2',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginRight: 6,
  },
  careerBadgeText: {
    color: '#6b7280',
    fontWeight: '600',
    fontSize: 14,
    letterSpacing: 0.2,
  },
  careerExactButton: {
    alignSelf: 'center',
    backgroundColor: '#fff',
    borderColor: '#1976d2',
    borderWidth: 2,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 12,
  },
  careerExactButtonText: {
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.3,
  },
  skillsSection: {
    marginTop: 48,
    paddingHorizontal: 32,
    backgroundColor: '#f8f9fa',
    paddingVertical: 40,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  skillsSectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 32,
    letterSpacing: -0.6,
  },
  skillsSectionSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  categoryTabsScroll: {
    marginBottom: 24,
  },
  categoryTabsContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    flexDirection: 'row',
  },
  categoryTab: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginHorizontal: 12,
    borderRadius: 24,
  },
  categoryTabSelected: {
    borderBottomWidth: 3,
    borderBottomColor: '#1976d2',
  },
  categoryTabText: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  categoryTabTextSelected: {
    color: '#1976d2',
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
  topicsScroll: {
    marginBottom: 32,
  },
  topicBubble: {
    backgroundColor: '#fff',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  topicBubbleSelected: {
    backgroundColor: '#4B4B63',
    borderColor: '#4B4B63',
  },
  topicBubbleText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  topicBubbleTextSelected: {
    color: '#fff',
  },
  topicBubbleLearners: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 4,
    fontWeight: '400',
    letterSpacing: 0.1,
  },
  topicBubbleLearnersSelected: {
    color: '#d1d5db',
  },
  coursesScroll: {
    marginBottom: 24,
  },
  courseCard: {
    width: 300,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginRight: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    overflow: 'hidden',
  },
  courseCardImage: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  courseCardContent: {
    padding: 16,
  },
  courseCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    lineHeight: 22,
    letterSpacing: -0.2,
  },
  courseCardInstructors: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
    fontWeight: '400',
    letterSpacing: 0.1,
  },
  courseCardRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  courseCardRatingText: {
    fontSize: 13,
    color: '#6b7280',
    marginRight: 6,
  },
  courseCardRatingCount: {
    fontSize: 13,
    color: '#9ca3af',
  },
  courseCardPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    letterSpacing: -0.2,
  },
  courseCardBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
  },
  badgeBestseller: {
    backgroundColor: '#1de9b6',
  },
  badgePremium: {
    backgroundColor: '#1976d2',
  },
  courseCardBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
  showAllButton: {
    alignSelf: 'center',
    backgroundColor: '#fff',
    borderColor: '#1976d2',
    borderWidth: 2,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  showAllButtonText: {
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.3,
  },
  slideIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  slideIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 5,
  },
  slideIndicatorActive: {
    backgroundColor: '#1976d2',
  },
  aboutSection: {
    marginTop: 48,
    paddingHorizontal: 32,
    backgroundColor: '#fff',
    paddingVertical: 60,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  aboutContainer: {
    alignItems: 'center',
    maxWidth: 800,
    alignSelf: 'center',
  },
  trustSection: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
    maxWidth: 200,
  },
  trustTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  trustLogos: {
    flexDirection: 'row',
    gap: 16,
  },
  trustLogo: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6b7280',
  },
  aboutGraphicContainer: {
    position: 'relative',
    width: 400,
    height: 400,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 60,
  },
  mainCircle: {
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: '#ff6b35',
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
  },
  mainCircleImage: {
    width: 200,
    height: 200,
    position: 'absolute',
    top: 50,
    left: 50,
  },
  avatar1: {
    position: 'absolute',
    top: 100,
    left: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatar2: {
    position: 'absolute',
    top: 200,
    right: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatar3: {
    position: 'absolute',
    bottom: 100,
    left: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatar4: {
    position: 'absolute',
    bottom: 200,
    right: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  aboutTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 24,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  aboutContent: {
    flex: 1,
    paddingLeft: 32,
    marginTop: 60,
    maxWidth: 500,
  },
  aboutSubtitle: {
    fontSize: 12,
    color: '#ff6b35',
    marginBottom: 8,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    borderBottomWidth: 2,
    borderBottomColor: '#ff6b35',
    alignSelf: 'flex-start',
    paddingBottom: 4,
  },
  aboutMainTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    lineHeight: 38,
  },
  aboutMission: {
    fontSize: 18,
    color: '#111827',
    marginBottom: 32,
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  aboutBelieveTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 12,
    lineHeight: 30,
  },
  aboutBelieveText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 16,
    lineHeight: 24,
  },
  aboutBullets: {
    marginBottom: 24,
  },
  aboutBullet: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
    lineHeight: 24,
  },
  aboutStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 40,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  statLabel: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
    textAlign: 'center',
    letterSpacing: 0.2,
  },
  statMission: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  learnMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff6b35',
    borderRadius: 6,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignSelf: 'flex-start',
  },
  learnMoreText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  learnMoreArrow: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 8,
  },
  dottedLine1: {
    position: 'absolute',
    top: 100,
    left: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dottedLine2: {
    position: 'absolute',
    top: 200,
    right: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dottedLine3: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dottedLine4: {
    position: 'absolute',
    bottom: 200,
    right: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    paddingHorizontal: 20,
  },
  testimonialsSection: {
    paddingVertical: 60,
    paddingHorizontal: 32,
    backgroundColor: '#f8f9fa',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  testimonialsContainer: {
    alignItems: 'center',
    maxWidth: 1000,
    alignSelf: 'center',
  },
  testimonialsTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    textAlign: 'center',
    letterSpacing: -0.6,
  },
  testimonialsSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    marginBottom: 48,
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 600,
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  testimonialsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 24,
    flexWrap: 'wrap',
  },
  testimonialCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    flex: 1,
    minWidth: 300,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  testimonialStars: {
    marginBottom: 16,
  },
  starIcon: {
    fontSize: 20,
  },
  testimonialText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 26,
    marginBottom: 20,
    fontStyle: 'italic',
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  testimonialAuthor: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  authorInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  authorRole: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
    letterSpacing: 0.1,
  },
  footer: {
    backgroundColor: '#111827',
    paddingVertical: 48,
    paddingHorizontal: 32,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    flexWrap: 'wrap',
    gap: 32,
  },
  footerSection: {
    flex: 1,
    minWidth: 200,
  },
  footerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  footerDescription: {
    fontSize: 16,
    color: '#9ca3af',
    lineHeight: 24,
    marginBottom: 16,
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  footerSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
    letterSpacing: -0.2,
  },
  footerLink: {
    fontSize: 16,
    color: '#9ca3af',
    marginBottom: 8,
    lineHeight: 24,
    fontWeight: '400',
    letterSpacing: 0.1,
  },
  footerBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#374151',
    flexWrap: 'wrap',
    gap: 16,
  },
  footerCopyright: {
    fontSize: 14,
    color: '#9ca3af',
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  footerSocial: {
    flexDirection: 'row',
    gap: 24,
  },
  socialLink: {
    fontSize: 14,
    color: '#9ca3af',
    fontWeight: '500',
    letterSpacing: 0.2,
  },
});
