import LoginModal from '@/components/LoginModal';
import MainNavbar from '@/components/MainNavbar';
import SignupModal from '@/components/SignupModal';
import React, { useEffect, useState } from 'react';
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View, useWindowDimensions } from 'react-native';

// Mock data for boot camps
const bootCamps = [
  {
    title: 'Startup Launch Boot Camp',
    image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    bgColor: '#E3F2FD',
    icon: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
    rating: 4.8,
    studentsCount: '120',
    duration: '12 weeks',
    startDate: 'March 15, 2024',
    description: 'Launch your startup with hands-on mentorship and real-world projects.'
  },
  {
    title: 'Digital Marketing Mastery',
    image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    bgColor: '#E8F4FD',
    icon: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    rating: 4.7,
    studentsCount: '85',
    duration: '10 weeks',
    startDate: 'April 1, 2024',
    description: 'Master digital marketing strategies to grow your business online.'
  },
  {
    title: 'Business Strategy & Growth',
    image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    bgColor: '#F0F8FF',
    icon: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
    rating: 4.6,
    studentsCount: '95',
    duration: '8 weeks',
    startDate: 'March 25, 2024',
    description: 'Develop advanced strategies for scaling and sustaining business growth.'
  },
];

const learningLevels = [
  'Discover',
  'Scale', 
  'Grow',
];

// Mock data for learning levels
const levelTabsData = [
  { name: 'Discover' },
  { name: 'Scale' },
  { name: 'Grow' },
];

// Mock courses per level
const levelCourses = {
  Discover: [
    { name: 'Business Model Canvas', courseTime: '2hr 30min' },
    { name: 'Market Research', courseTime: '4hr 15min' },
    { name: 'Financial Planning', courseTime: '6hr 45min' },
  ],
  Scale: [
    { name: 'Customer Acquisition', courseTime: '3hr 20min' },
    { name: 'Pitch Deck Creation', courseTime: '5hr 10min' },
    { name: 'Social Media Marketing', courseTime: '3hr 45min' },
  ],
  Grow: [
    { name: 'E-commerce Strategy', courseTime: '2hr 15min' },
    { name: 'Business Strategy & Growth Hacking', courseTime: '4hr 30min' },
    { name: 'Financial Management for Entrepreneurs', courseTime: '3hr 50min' },
  ],
};

const popularCourses = [
  { name: 'Business Model Canvas', courseTime: '2hr 30min', isSelected: true },
  { name: 'Market Research', courseTime: '4hr 15min', isSelected: false },
  { name: 'Financial Planning', courseTime: '6hr 45min', isSelected: false },
  { name: 'Customer Acquisition', courseTime: '3hr 20min', isSelected: false },
  { name: 'Pitch Deck Creation', courseTime: '5hr 10min', isSelected: false },
  { name: 'Social Media Marketing', courseTime: '3hr 45min', isSelected: false },
  { name: 'E-commerce Strategy', courseTime: '2hr 15min', isSelected: false },
];

// Mock featured courses per level
const featuredCoursesByLevel: { [key: string]: { title: string; instructors: string; rating: number; ratingsCount: string; price: string; badge: string; image: { uri: string } }[] } = {
  Discover: [
    {
      title: 'Business Model Canvas Essentials',
      instructors: 'John Smith, Sarah Johnson',
      rating: 4.8,
      ratingsCount: '1,200',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Market Research Fundamentals',
      instructors: 'Emily Rodriguez',
      rating: 4.7,
      ratingsCount: '950',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Financial Planning Basics',
      instructors: 'Alex Thompson',
      rating: 4.6,
      ratingsCount: '800',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Entrepreneurial Mindset Mastery',
      instructors: 'Lisa Park',
      rating: 4.7,
      ratingsCount: '1,100',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Idea Validation Bootcamp',
      instructors: 'Chris Lee',
      rating: 4.9,
      ratingsCount: '1,500',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
    },
  ],
  Scale: [
    {
      title: 'Customer Acquisition Strategies',
      instructors: 'Alex Thompson',
      rating: 4.6,
      ratingsCount: '800',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Pitch Deck Creation Masterclass',
      instructors: 'Lisa Park',
      rating: 4.7,
      ratingsCount: '1,100',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Social Media Marketing Pro',
      instructors: 'Maria Garcia',
      rating: 4.8,
      ratingsCount: '1,300',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Growth Hacking Bootcamp',
      instructors: 'David Wilson',
      rating: 4.7,
      ratingsCount: '1,050',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Brand Building Essentials',
      instructors: 'Sarah Johnson',
      rating: 4.5,
      ratingsCount: '900',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    },
  ],
  Grow: [
    {
      title: 'E-commerce Strategy Advanced',
      instructors: 'Chris Lee',
      rating: 4.9,
      ratingsCount: '1,500',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Business Strategy & Growth Hacking',
      instructors: 'Maria Garcia',
      rating: 4.8,
      ratingsCount: '1,300',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1515168833906-d2a3b82b302b?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Financial Management for Entrepreneurs',
      instructors: 'Emily Rodriguez',
      rating: 4.7,
      ratingsCount: '1,200',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Leadership for Founders',
      instructors: 'Alex Thompson',
      rating: 4.6,
      ratingsCount: '1,100',
      price: 'Free',
      badge: 'Bestseller',
      image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    },
    {
      title: 'Exit Strategies Bootcamp',
      instructors: 'Lisa Park',
      rating: 4.5,
      ratingsCount: '950',
      price: 'Free',
      badge: 'Premium',
      image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
    },
  ],
};

// Mock data for slider
const sliderData = [
  {
    title: 'Build Your Business Empire',
    subtitle: 'Master entrepreneurial skills and launch successful ventures. Start your journey with Imuka Juniors.',
    image: { uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Join Our Boot Camps',
    subtitle: 'Intensive programs designed for entrepreneurs. Learn from successful business leaders.',
    image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Progress Through Levels',
    subtitle: 'Discover → Scale → Grow. Structured path for your entrepreneurial success.',
    image: { uri: 'https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Network with Innovators',
    subtitle: 'Connect with like-minded entrepreneurs and mentors to grow your business.',
    image: { uri: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?auto=format&fit=crop&w=800&q=80' },
  },
  {
    title: 'Access Real-World Resources',
    subtitle: 'Get hands-on with tools, templates, and case studies for your startup.',
    image: { uri: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?auto=format&fit=crop&w=800&q=80' },
  },
];

// Learning levels data with title, description, image, and courses
const learningLevelsData: { [key: string]: { title: string; description: string; image: { uri: string }; courses: { name: string; courseTime: string }[] } } = {
  Discover: {
    title: 'Discover',
    description: 'Start your entrepreneurial journey by learning the fundamentals of business, market research, and planning.',
    image: { uri: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80' },
    courses: [
      { name: 'Business Model Canvas', courseTime: '2hr 30min' },
      { name: 'Market Research', courseTime: '4hr 15min' },
      { name: 'Financial Planning', courseTime: '6hr 45min' },
      { name: 'Entrepreneurial Mindset', courseTime: '3hr 10min' },
      { name: 'Idea Validation', courseTime: '2hr 50min' },
    ],
  },
  Scale: {
    title: 'Scale',
    description: 'Take your business to the next level with customer acquisition, marketing, and pitching skills.',
    image: { uri: 'https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=800&q=80' },
    courses: [
      { name: 'Customer Acquisition', courseTime: '3hr 20min' },
      { name: 'Pitch Deck Creation', courseTime: '5hr 10min' },
      { name: 'Social Media Marketing', courseTime: '3hr 45min' },
      { name: 'Growth Hacking', courseTime: '4hr 00min' },
      { name: 'Brand Building', courseTime: '2hr 40min' },
    ],
  },
  Grow: {
    title: 'Grow',
    description: 'Master advanced strategies for scaling, e-commerce, and financial management for entrepreneurs.',
    image: { uri: 'https://images.unsplash.com/photo-1503676382389-4809596d5290?auto=format&fit=crop&w=800&q=80' },
    courses: [
      { name: 'E-commerce Strategy', courseTime: '2hr 15min' },
      { name: 'Business Strategy & Growth Hacking', courseTime: '4hr 30min' },
      { name: 'Financial Management for Entrepreneurs', courseTime: '3hr 50min' },
      { name: 'Leadership for Founders', courseTime: '3hr 20min' },
      { name: 'Exit Strategies', courseTime: '2hr 55min' },
    ],
  },
};

// Enhanced responsive design system
const BREAKPOINTS = {
  mobile: 700,
  tablet: 1000,
  desktop: 1200,
  largeDesktop: 1440
};

// Typography scale with improved hierarchy
const TYPOGRAPHY_SCALE = {
  // Display text (hero sections)
  display: {
    mobile: { fontSize: 28, lineHeight: 34, letterSpacing: -0.8, fontWeight: '700' },
    tablet: { fontSize: 40, lineHeight: 48, letterSpacing: -1.0, fontWeight: '700' },
    desktop: { fontSize: 48, lineHeight: 56, letterSpacing: -1.2, fontWeight: '700' }
  },
  // Main headings (section titles)
  heading1: {
    mobile: { fontSize: 24, lineHeight: 30, letterSpacing: -0.6, fontWeight: '700' },
    tablet: { fontSize: 32, lineHeight: 38, letterSpacing: -0.8, fontWeight: '700' },
    desktop: { fontSize: 36, lineHeight: 44, letterSpacing: -0.9, fontWeight: '700' }
  },
  // Sub headings
  heading2: {
    mobile: { fontSize: 20, lineHeight: 26, letterSpacing: -0.4, fontWeight: '600' },
    tablet: { fontSize: 24, lineHeight: 30, letterSpacing: -0.5, fontWeight: '600' },
    desktop: { fontSize: 28, lineHeight: 34, letterSpacing: -0.6, fontWeight: '600' }
  },
  // Card titles, smaller headings
  heading3: {
    mobile: { fontSize: 18, lineHeight: 24, letterSpacing: -0.2, fontWeight: '600' },
    tablet: { fontSize: 20, lineHeight: 26, letterSpacing: -0.3, fontWeight: '600' },
    desktop: { fontSize: 22, lineHeight: 28, letterSpacing: -0.4, fontWeight: '600' }
  },
  // Subtitles, descriptions
  subtitle: {
    mobile: { fontSize: 16, lineHeight: 24, letterSpacing: 0.1, fontWeight: '400' },
    tablet: { fontSize: 18, lineHeight: 28, letterSpacing: 0.1, fontWeight: '400' },
    desktop: { fontSize: 20, lineHeight: 30, letterSpacing: 0.1, fontWeight: '400' }
  },
  // Body text
  body: {
    mobile: { fontSize: 16, lineHeight: 24, letterSpacing: 0.2, fontWeight: '400' },
    tablet: { fontSize: 17, lineHeight: 26, letterSpacing: 0.2, fontWeight: '400' },
    desktop: { fontSize: 18, lineHeight: 28, letterSpacing: 0.2, fontWeight: '400' }
  },
  // Small text, captions
  caption: {
    mobile: { fontSize: 14, lineHeight: 20, letterSpacing: 0.3, fontWeight: '400' },
    tablet: { fontSize: 15, lineHeight: 22, letterSpacing: 0.3, fontWeight: '400' },
    desktop: { fontSize: 16, lineHeight: 24, letterSpacing: 0.3, fontWeight: '400' }
  },
  // Button text
  button: {
    mobile: { fontSize: 15, lineHeight: 20, letterSpacing: 0.4, fontWeight: '600' },
    tablet: { fontSize: 16, lineHeight: 22, letterSpacing: 0.4, fontWeight: '600' },
    desktop: { fontSize: 17, lineHeight: 24, letterSpacing: 0.4, fontWeight: '600' }
  }
};

// Spacing scale for consistent rhythm
const SPACING_SCALE = {
  // Section spacing (between major sections)
  section: {
    mobile: { marginTop: 24, marginBottom: 24, paddingVertical: 20 },
    tablet: { marginTop: 40, marginBottom: 40, paddingVertical: 32 },
    desktop: { marginTop: 56, marginBottom: 56, paddingVertical: 48 }
  },
  // Component spacing (between components within sections)
  component: {
    mobile: { marginTop: 16, marginBottom: 16, paddingVertical: 12 },
    tablet: { marginTop: 24, marginBottom: 24, paddingVertical: 20 },
    desktop: { marginTop: 32, marginBottom: 32, paddingVertical: 28 }
  },
  // Element spacing (between text elements)
  element: {
    mobile: { marginBottom: 12 },
    tablet: { marginBottom: 16 },
    desktop: { marginBottom: 20 }
  },
  // Tight spacing (for closely related elements)
  tight: {
    mobile: { marginBottom: 8 },
    tablet: { marginBottom: 10 },
    desktop: { marginBottom: 12 }
  },
  // Container padding
  container: {
    mobile: { paddingHorizontal: 16 },
    tablet: { paddingHorizontal: 24 },
    desktop: { paddingHorizontal: 32 }
  }
};

export default function HomeScreen() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedLevel, setSelectedLevel] = useState('Discover');
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { width: windowWidth } = useWindowDimensions();

  // Enhanced breakpoint detection
  const isMobile = windowWidth < BREAKPOINTS.mobile;
  const isTablet = windowWidth >= BREAKPOINTS.mobile && windowWidth < BREAKPOINTS.tablet;
  const isDesktop = windowWidth >= BREAKPOINTS.tablet && windowWidth < BREAKPOINTS.largeDesktop;
  const isLargeDesktop = windowWidth >= BREAKPOINTS.largeDesktop;

  const [showLogin, setShowLogin] = useState(false);
  const [showSignup, setShowSignup] = useState(false);

  // Auto-scroll functionality
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % sliderData.length);
    }, 5000); // Change slide every 5 seconds

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % sliderData.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + sliderData.length) % sliderData.length);
  };

  // Enhanced typography utilities
  const getTypographyStyle = (variant: keyof typeof TYPOGRAPHY_SCALE) => {
    if (isMobile) return TYPOGRAPHY_SCALE[variant].mobile;
    if (isTablet) return TYPOGRAPHY_SCALE[variant].tablet;
    return TYPOGRAPHY_SCALE[variant].desktop;
  };

  // Enhanced spacing utilities
  const getSpacingStyle = (variant: keyof typeof SPACING_SCALE) => {
    if (isMobile) return SPACING_SCALE[variant].mobile;
    if (isTablet) return SPACING_SCALE[variant].tablet;
    return SPACING_SCALE[variant].desktop;
  };

  // Responsive container width
  const getContainerStyle = () => {
    const baseStyle = {
      width: '100%',
      alignSelf: 'center' as const,
      ...getSpacingStyle('container')
    };

    if (isLargeDesktop) return { ...baseStyle, maxWidth: BREAKPOINTS.largeDesktop };
    if (isDesktop) return { ...baseStyle, maxWidth: BREAKPOINTS.desktop };
    if (isTablet) return { ...baseStyle, maxWidth: BREAKPOINTS.tablet };
    return baseStyle;
  };

  // Enhanced banner utilities
  const getBannerStyle = () => {
    if (isMobile) return { paddingVertical: 12 };
    if (isTablet) return { paddingVertical: 16 };
    return { paddingVertical: 20 };
  };

  const getBannerButtonStyle = () => {
    if (isMobile) return { paddingVertical: 6, paddingHorizontal: 12 };
    if (isTablet) return { paddingVertical: 8, paddingHorizontal: 16 };
    return { paddingVertical: 10, paddingHorizontal: 20 };
  };

  return (
    <>
      <LoginModal open={showLogin} onClose={() => setShowLogin(false)} />
      <SignupModal open={showSignup} onClose={() => setShowSignup(false)} />
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.contentContainer}>
        {/* Top Banner */}
        <View style={[
          styles.topBanner,
          getBannerStyle(),
          {
            shadowColor: '#000',
            shadowOpacity: 0.08,
            shadowRadius: 6,
            elevation: 2,
            borderBottomWidth: 1,
            borderBottomColor: '#1565c0',
            marginTop: 0,
            flexDirection: isMobile ? 'column' : 'row',
            justifyContent: 'center',
            alignItems: 'center',
            gap: isMobile ? 8 : 12,
          },
        ]}>
          <Text style={[
            styles.topBannerText,
            getTypographyStyle('button'),
            {
              textAlign: 'center',
              color: '#fff',
              marginBottom: 0,
              alignSelf: 'center'
            }
          ]}>
            Ready to start your entrepreneurial journey?
          </Text>
          <TouchableOpacity style={[
            {
              backgroundColor: '#fff',
              borderRadius: isMobile ? 12 : 16,
              alignSelf: 'center',
              shadowColor: '#000',
              shadowOpacity: 0.1,
              shadowRadius: 4,
              elevation: 2,
            },
            getBannerButtonStyle()
          ]}>
            <Text style={[
              styles.topBannerButtonText,
              getTypographyStyle('button'),
              { color: '#1976d2' }
            ]}>
              Join Imuka Juniors Boot Camps
            </Text>
          </TouchableOpacity>
        </View>

        {/* Header / Navbar */}
        <MainNavbar onShowLogin={() => setShowLogin(true)} onShowSignup={() => setShowSignup(true)} />

        {/* Hero Section */}
        <View style={[
          styles.heroSliderWrap,
          getContainerStyle(),
          {
            flexDirection: 'row',
            minHeight: isMobile ? 320 : isTablet ? 400 : 480,
            borderRadius: isMobile ? 12 : 20,
            overflow: 'hidden',
            ...getSpacingStyle('section'),
          },
        ]}>
          {/* Full background image */}
          <Image source={sliderData[currentSlide].image} style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            resizeMode: 'cover',
            zIndex: 0,
          }} />
          {/* Enhanced overlay for better readability */}
          <View style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(17,24,39,0.4)',
            zIndex: 1,
          }} />

          <View style={[styles.heroSliderContent, {
            zIndex: 2,
            justifyContent: 'center',
            alignItems: isMobile ? 'center' : 'flex-start',
            paddingHorizontal: isMobile ? 20 : 40,
            paddingVertical: isMobile ? 24 : 32,
            width: '100%',
          }]}>
            {/* Enhanced content card */}
            <View style={{
              backgroundColor: 'rgba(255,255,255,0.95)',
              borderRadius: isMobile ? 16 : 24,
              paddingVertical: isMobile ? 24 : 40,
              paddingHorizontal: isMobile ? 20 : 32,
              maxWidth: isMobile ? '100%' : 520,
              marginLeft: isMobile ? 0 : 0,
              shadowColor: '#000',
              shadowOpacity: 0.15,
              shadowRadius: 20,
              elevation: 6,
              borderWidth: 1,
              borderColor: 'rgba(255,255,255,0.2)',
            }}>
              <Text style={[
                getTypographyStyle('display'),
                {
                  color: '#111827',
                  textAlign: isMobile ? 'center' : 'left',
                  ...getSpacingStyle('element')
                }
              ]}>
                {sliderData[currentSlide].title}
              </Text>
              <Text style={[
                getTypographyStyle('subtitle'),
                {
                  color: '#6b7280',
                  textAlign: isMobile ? 'center' : 'left'
                }
              ]}>
                {sliderData[currentSlide].subtitle}
              </Text>
            </View>
          </View>
        </View>

        {/* Slide Indicators */}
        <View style={[
          styles.slideIndicators,
          {
            marginTop: isMobile ? 16 : 24,
            marginBottom: isMobile ? 8 : 16,
          }
        ]}>
          {sliderData.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.slideIndicator,
                {
                  width: isMobile ? 8 : 10,
                  height: isMobile ? 8 : 10,
                  borderRadius: isMobile ? 4 : 5,
                  marginHorizontal: isMobile ? 4 : 6,
                },
                index === currentSlide && styles.slideIndicatorActive
              ]}
              onPress={() => setCurrentSlide(index)}
            />
          ))}
        </View>

        {/* About Us Section */}
        <View style={[
          styles.aboutSection,
          getContainerStyle(),
          getSpacingStyle('section'),
          {
            backgroundColor: '#fff',
            borderRadius: isMobile ? 0 : 24,
          }
        ]}>
          <View style={[
            styles.aboutContainer,
            {
              alignItems: 'center',
              maxWidth: isMobile ? '100%' : 900,
              alignSelf: 'center',
            }
          ]}>
            <Text style={[
              getTypographyStyle('heading1'),
              {
                textAlign: 'center',
                color: '#111827',
                ...getSpacingStyle('element')
              }
            ]}>
              About Imuka Juniors
            </Text>
            <Text style={[
              getTypographyStyle('body'),
              {
                textAlign: 'center',
                color: '#6b7280',
                maxWidth: isMobile ? '100%' : 700,
                ...getSpacingStyle('component')
              }
            ]}>
              Imuka Juniors is dedicated to empowering entrepreneurs with the skills and knowledge needed to build successful businesses. We provide comprehensive boot camps and structured learning programs that combine practical experience with proven business strategies to accelerate your entrepreneurial journey.
            </Text>

            <View style={[
              styles.statsContainer,
              {
                flexDirection: isMobile ? 'column' : 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                width: '100%',
                maxWidth: isMobile ? 300 : 800,
                gap: isMobile ? 24 : 16,
                marginTop: isMobile ? 32 : 48,
              }
            ]}>
              <View style={[styles.statItem, { alignItems: 'center', flex: isMobile ? 0 : 1 }]}>
                <Text style={[
                  styles.statNumber,
                  {
                    fontSize: isMobile ? 32 : 40,
                    fontWeight: '700',
                    color: '#1976d2',
                    marginBottom: 8,
                  }
                ]}>500+</Text>
                <Text style={[
                  getTypographyStyle('caption'),
                  {
                    textAlign: 'center',
                    color: '#6b7280',
                    fontWeight: '500'
                  }
                ]}>Entrepreneurs</Text>
              </View>
              <View style={[styles.statItem, { alignItems: 'center', flex: isMobile ? 0 : 1 }]}>
                <Text style={[
                  styles.statNumber,
                  {
                    fontSize: isMobile ? 32 : 40,
                    fontWeight: '700',
                    color: '#1976d2',
                    marginBottom: 8,
                  }
                ]}>15+</Text>
                <Text style={[
                  getTypographyStyle('caption'),
                  {
                    textAlign: 'center',
                    color: '#6b7280',
                    fontWeight: '500'
                  }
                ]}>Boot Camps</Text>
              </View>
              <View style={[styles.statItem, { alignItems: 'center', flex: isMobile ? 0 : 1 }]}>
                <Text style={[
                  styles.statNumber,
                  {
                    fontSize: isMobile ? 32 : 40,
                    fontWeight: '700',
                    color: '#1976d2',
                    marginBottom: 8,
                  }
                ]}>95%</Text>
                <Text style={[
                  getTypographyStyle('caption'),
                  {
                    textAlign: 'center',
                    color: '#6b7280',
                    fontWeight: '500'
                  }
                ]}>Success Rate</Text>
              </View>
              <View style={[styles.statItem, { alignItems: 'center', flex: isMobile ? 0 : 1 }]}>
                <Text style={[
                  styles.statNumber,
                  {
                    fontSize: isMobile ? 32 : 40,
                    fontWeight: '700',
                    color: '#1976d2',
                    marginBottom: 8,
                  }
                ]}>50+</Text>
                <Text style={[
                  getTypographyStyle('caption'),
                  {
                    textAlign: 'center',
                    color: '#6b7280',
                    fontWeight: '500'
                  }
                ]}>Business Mentors</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Learning Levels Section */}
        <View style={[
          getContainerStyle(),
          getSpacingStyle('section'),
          {
            backgroundColor: '#f8f9fa',
            borderRadius: isMobile ? 0 : 24,
            paddingVertical: isMobile ? 32 : 48,
          }
        ]}>
          {/* Section Title & Subtitle */}
          <Text style={[
            getTypographyStyle('heading1'),
            {
              color: '#111827',
              textAlign: 'center',
              ...getSpacingStyle('element')
            }
          ]}>
            All the skills you need in one place
          </Text>
          <Text style={[
            getTypographyStyle('subtitle'),
            {
              color: '#6b7280',
              maxWidth: isMobile ? '100%' : 700,
              alignSelf: 'center',
              textAlign: 'center',
              ...getSpacingStyle('component')
            }
          ]}>
            From critical skills to technical topics, Imuka Juniors supports your professional development.
          </Text>
          {/* Enhanced Level Tabs */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ marginBottom: isMobile ? 16 : 24 }}
            contentContainerStyle={{
              alignItems: 'center',
              paddingHorizontal: isMobile ? 16 : 24,
              justifyContent: isMobile ? 'flex-start' : 'center',
              minWidth: isMobile ? undefined : '100%'
            }}
          >
            {levelTabsData.map((tab, idx) => (
              <TouchableOpacity
                key={idx}
                style={{
                  paddingHorizontal: isMobile ? 16 : 24,
                  paddingVertical: isMobile ? 12 : 16,
                  marginRight: isMobile ? 8 : 12,
                  borderBottomWidth: selectedLevel === tab.name ? 3 : 0,
                  borderBottomColor: selectedLevel === tab.name ? '#1976d2' : 'transparent',
                  backgroundColor: selectedLevel === tab.name ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                  borderRadius: isMobile ? 8 : 12,
                  minWidth: isMobile ? 80 : 100,
                  alignItems: 'center',
                }}
                onPress={() => setSelectedLevel(tab.name)}
              >
                <Text style={[
                  getTypographyStyle('button'),
                  {
                    fontWeight: selectedLevel === tab.name ? '700' : '600',
                    color: selectedLevel === tab.name ? '#1976d2' : '#6b7280',
                  }
                ]}>{tab.name}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
          {/* Enhanced Topic Bubbles */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{ marginBottom: isMobile ? 20 : 32 }}
            contentContainerStyle={{
              alignItems: 'center',
              paddingHorizontal: isMobile ? 16 : 24
            }}
          >
            {learningLevelsData[selectedLevel].courses.map((course: any, idx: number) => (
              <View key={idx} style={{
                backgroundColor: idx === 0 ? '#1976d2' : '#fff',
                borderRadius: isMobile ? 20 : 28,
                paddingHorizontal: isMobile ? 16 : 24,
                paddingVertical: isMobile ? 12 : 16,
                marginRight: isMobile ? 12 : 16,
                alignItems: 'center',
                borderWidth: idx === 0 ? 0 : 2,
                borderColor: '#e5e7eb',
                shadowColor: '#000',
                shadowOpacity: idx === 0 ? 0.15 : 0.08,
                shadowRadius: idx === 0 ? 8 : 4,
                elevation: idx === 0 ? 4 : 2,
                minWidth: isMobile ? 120 : 140,
              }}>
                <Text style={[
                  getTypographyStyle('caption'),
                  {
                    color: idx === 0 ? '#fff' : '#111827',
                    fontWeight: '600',
                    textAlign: 'center',
                    marginBottom: 4,
                  }
                ]}>{course.name}</Text>
                <Text style={[
                  {
                    fontSize: isMobile ? 11 : 12,
                    color: idx === 0 ? 'rgba(255,255,255,0.8)' : '#6b7280',
                    textAlign: 'center',
                    fontWeight: '400',
                  }
                ]}>{(idx + 1) * 2}K+ learners</Text>
              </View>
            ))}
          </ScrollView>
          {/* Enhanced Course Cards */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={{
              marginBottom: isMobile ? 20 : 32,
              width: '100%',
              alignSelf: 'center'
            }}
            contentContainerStyle={{
              paddingHorizontal: isMobile ? 16 : 24,
              paddingRight: isMobile ? 32 : 48,
            }}
          >
            {featuredCoursesByLevel[selectedLevel].map((course: any, idx: number) => (
              <View key={idx} style={{
                width: isMobile ? 280 : 340,
                backgroundColor: '#fff',
                borderRadius: isMobile ? 16 : 20,
                marginRight: isMobile ? 16 : 24,
                shadowColor: '#000',
                shadowOpacity: 0.12,
                shadowRadius: 12,
                elevation: 4,
                borderWidth: 1,
                borderColor: '#f1f3f4',
                overflow: 'hidden',
              }}>
                <Image
                  source={course.image}
                  style={{
                    width: '100%',
                    height: isMobile ? 140 : 180,
                    borderTopLeftRadius: isMobile ? 16 : 20,
                    borderTopRightRadius: isMobile ? 16 : 20
                  }}
                />
                <View style={{ padding: isMobile ? 16 : 20 }}>
                  <Text style={[
                    getTypographyStyle('heading3'),
                    {
                      color: '#111827',
                      marginBottom: 8,
                      textAlign: 'left',
                      lineHeight: isMobile ? 22 : 26,
                    }
                  ]}>{course.title}</Text>
                  <Text style={[
                    getTypographyStyle('caption'),
                    {
                      color: '#6b7280',
                      marginBottom: 12,
                      textAlign: 'left'
                    }
                  ]}>{course.instructors}</Text>
                  <View style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    marginBottom: 12
                  }}>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#f59e0b',
                        marginRight: 6,
                        fontWeight: '600'
                      }
                    ]}>⭐ {course.rating}</Text>
                    <Text style={[
                      getTypographyStyle('caption'),
                      { color: '#9ca3af' }
                    ]}>({course.ratingsCount})</Text>
                  </View>
                  <Text style={[
                    getTypographyStyle('heading3'),
                    {
                      color: '#111827',
                      marginBottom: 12,
                      textAlign: 'left'
                    }
                  ]}>{course.price}</Text>
                  <View style={{
                    alignSelf: 'flex-start',
                    paddingHorizontal: 12,
                    paddingVertical: 6,
                    borderRadius: 8,
                    backgroundColor: course.badge === 'Bestseller' ? '#10b981' : '#1976d2'
                  }}>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#fff',
                        fontWeight: '600'
                      }
                    ]}>{course.badge}</Text>
                  </View>
                </View>
              </View>
            ))}
          </ScrollView>
          <TouchableOpacity style={[
            styles.showAllButton,
            {
              marginTop: isMobile ? 16 : 24,
              paddingHorizontal: isMobile ? 20 : 32,
              paddingVertical: isMobile ? 12 : 16,
              borderRadius: isMobile ? 12 : 16,
              backgroundColor: '#fff',
              borderWidth: 2,
              borderColor: '#1976d2',
              shadowColor: '#000',
              shadowOpacity: 0.08,
              shadowRadius: 8,
              elevation: 3,
            }
          ]}>
            <Text style={[
              getTypographyStyle('button'),
              {
                color: '#1976d2',
                fontWeight: '600'
              }
            ]}>Show all Entrepreneurship courses</Text>
          </TouchableOpacity>
        </View>

        {/* Boot Camps Section */}
        <View style={[
          getContainerStyle(),
          getSpacingStyle('section'),
          {
            backgroundColor: '#f8f9fa',
            borderRadius: isMobile ? 0 : 24,
            paddingVertical: isMobile ? 32 : 48,
          }
        ]}>
          {/* Section Header */}
          <View style={{
            alignItems: 'center',
            marginBottom: isMobile ? 24 : 40,
            paddingHorizontal: isMobile ? 0 : 20,
          }}>
            <Text style={[
              getTypographyStyle('heading1'),
              {
                textAlign: 'center',
                color: '#111827',
                marginBottom: isMobile ? 12 : 16,
              }
            ]}>Ready to join our boot camps?</Text>
            <Text style={[
              getTypographyStyle('subtitle'),
              {
                textAlign: 'center',
                color: '#6b7280',
                maxWidth: isMobile ? '100%' : 700,
                lineHeight: isMobile ? 24 : 28,
              }
            ]}>Intensive programs designed for entrepreneurs. Learn from successful business leaders and accelerate your startup journey.</Text>
          </View>
          {/* Boot Camp Cards - Responsive Layout */}
          {isMobile ? (
            // Mobile: Vertical scrolling cards
            <View style={{
              marginBottom: 24,
              paddingHorizontal: 0,
            }}>
              {bootCamps.map((c, idx) => (
                <View key={idx} style={{
                  backgroundColor: '#fff',
                  borderRadius: 16,
                  marginBottom: 16,
                  shadowColor: '#000',
                  shadowOpacity: 0.08,
                  shadowRadius: 8,
                  elevation: 3,
                  borderWidth: 1,
                  borderColor: '#e5e7eb',
                  overflow: 'hidden',
                }}>
                  <Image
                    source={c.image}
                    style={{
                      width: '100%',
                      height: 160,
                      resizeMode: 'cover',
                    }}
                  />
                  <View style={{
                    padding: 20,
                  }}>
                    <Text style={[
                      getTypographyStyle('heading3'),
                      {
                        color: '#111827',
                        marginBottom: 8,
                        lineHeight: 24,
                      }
                    ]}>{c.title}</Text>
                    <Text style={[
                      getTypographyStyle('body'),
                      {
                        color: '#6b7280',
                        marginBottom: 16,
                        lineHeight: 24,
                      }
                    ]}>{c.description}</Text>

                    {/* Info Row */}
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      marginBottom: 16,
                      paddingVertical: 12,
                      paddingHorizontal: 16,
                      backgroundColor: '#f8f9fa',
                      borderRadius: 12,
                    }}>
                      <View style={{ flex: 1 }}>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#9ca3af',
                            marginBottom: 4,
                            fontSize: 12,
                          }
                        ]}>Start Date</Text>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#374151',
                            fontWeight: '600',
                            fontSize: 14,
                          }
                        ]}>{c.startDate}</Text>
                      </View>
                      <View style={{ flex: 1, alignItems: 'flex-end' }}>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#9ca3af',
                            marginBottom: 4,
                            fontSize: 12,
                          }
                        ]}>Duration</Text>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#374151',
                            fontWeight: '600',
                            fontSize: 14,
                          }
                        ]}>{c.duration}</Text>
                      </View>
                    </View>

                    {/* Rating and Students */}
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      marginBottom: 16,
                    }}>
                      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Text style={{ fontSize: 16, marginRight: 4 }}>⭐</Text>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#374151',
                            fontWeight: '600',
                            marginRight: 8,
                          }
                        ]}>{c.rating}</Text>
                        <Text style={[
                          getTypographyStyle('caption'),
                          { color: '#9ca3af' }
                        ]}>({c.studentsCount} students)</Text>
                      </View>
                    </View>

                    {/* CTA Button */}
                    <TouchableOpacity style={{
                      backgroundColor: '#1976d2',
                      borderRadius: 12,
                      paddingVertical: 12,
                      paddingHorizontal: 20,
                      alignItems: 'center',
                      shadowColor: '#1976d2',
                      shadowOpacity: 0.3,
                      shadowRadius: 8,
                      elevation: 4,
                    }}>
                      <Text style={[
                        getTypographyStyle('button'),
                        {
                          color: '#fff',
                          fontWeight: '600'
                        }
                      ]}>Join Boot Camp</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </View>
          ) : (
            // Desktop: Horizontal scrolling cards
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{
                marginBottom: 32,
                width: '100%',
              }}
              contentContainerStyle={{
                paddingHorizontal: 24,
                paddingRight: 48,
              }}
            >
              {bootCamps.map((c, idx) => (
                <View key={idx} style={{
                  width: 380,
                  backgroundColor: '#fff',
                  borderRadius: 20,
                  marginRight: 24,
                  shadowColor: '#000',
                  shadowOpacity: 0.12,
                  shadowRadius: 16,
                  elevation: 6,
                  borderWidth: 1,
                  borderColor: '#e5e7eb',
                  overflow: 'hidden',
                }}>
                  <Image
                    source={c.image}
                    style={{
                      width: '100%',
                      height: 220,
                      resizeMode: 'cover',
                    }}
                  />
                  <View style={{
                    padding: 24,
                  }}>
                    <Text style={[
                      getTypographyStyle('heading2'),
                      {
                        color: '#111827',
                        marginBottom: 12,
                        lineHeight: 28,
                      }
                    ]}>{c.title}</Text>
                    <Text style={[
                      getTypographyStyle('body'),
                      {
                        color: '#6b7280',
                        marginBottom: 20,
                        lineHeight: 26,
                      }
                    ]}>{c.description}</Text>

                    {/* Info Grid */}
                    <View style={{
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                      marginBottom: 20,
                      paddingVertical: 16,
                      paddingHorizontal: 20,
                      backgroundColor: '#f8f9fa',
                      borderRadius: 16,
                    }}>
                      <View style={{ alignItems: 'center' }}>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#9ca3af',
                            marginBottom: 4,
                          }
                        ]}>Start Date</Text>
                        <Text style={[
                          getTypographyStyle('body'),
                          {
                            color: '#374151',
                            fontWeight: '600',
                          }
                        ]}>{c.startDate}</Text>
                      </View>
                      <View style={{ alignItems: 'center' }}>
                        <Text style={[
                          getTypographyStyle('caption'),
                          {
                            color: '#9ca3af',
                            marginBottom: 4,
                          }
                        ]}>Duration</Text>
                        <Text style={[
                          getTypographyStyle('body'),
                          {
                            color: '#374151',
                            fontWeight: '600',
                          }
                        ]}>{c.duration}</Text>
                      </View>
                    </View>

                    {/* Rating and Students */}
                    <View style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                      marginBottom: 20,
                    }}>
                      <Text style={{ fontSize: 18, marginRight: 6 }}>⭐</Text>
                      <Text style={[
                        getTypographyStyle('body'),
                        {
                          color: '#374151',
                          fontWeight: '600',
                          marginRight: 12,
                        }
                      ]}>{c.rating}</Text>
                      <Text style={[
                        getTypographyStyle('body'),
                        { color: '#9ca3af' }
                      ]}>({c.studentsCount} students)</Text>
                    </View>

                    {/* CTA Button */}
                    <TouchableOpacity style={{
                      backgroundColor: '#1976d2',
                      borderRadius: 16,
                      paddingVertical: 16,
                      paddingHorizontal: 24,
                      alignItems: 'center',
                      shadowColor: '#1976d2',
                      shadowOpacity: 0.3,
                      shadowRadius: 12,
                      elevation: 6,
                    }}>
                      <Text style={[
                        getTypographyStyle('button'),
                        {
                          color: '#fff',
                          fontWeight: '600',
                          fontSize: 16,
                        }
                      ]}>Join Boot Camp</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ))}
            </ScrollView>
          )}
          {/* View All Button */}
          <View style={{
            alignItems: 'center',
            marginTop: isMobile ? 8 : 16,
          }}>
            <TouchableOpacity style={{
              backgroundColor: 'transparent',
              borderWidth: 2,
              borderColor: '#1976d2',
              borderRadius: isMobile ? 12 : 16,
              paddingHorizontal: isMobile ? 24 : 32,
              paddingVertical: isMobile ? 12 : 16,
              alignItems: 'center',
              shadowColor: '#1976d2',
              shadowOpacity: 0.1,
              shadowRadius: 8,
              elevation: 2,
            }}>
              <Text style={[
                getTypographyStyle('button'),
                {
                  color: '#1976d2',
                  fontWeight: '600'
                }
              ]}>View All Boot Camps</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Testimonials Section */}
        <View style={[
          getContainerStyle(),
          getSpacingStyle('section'),
          {
            backgroundColor: '#f8f9fa',
            borderRadius: isMobile ? 0 : 24,
            paddingVertical: isMobile ? 40 : 60,
          }
        ]}>
          <View style={[
            styles.testimonialsContainer,
            {
              alignItems: 'center',
              maxWidth: isMobile ? '100%' : 1000,
              alignSelf: 'center',
            }
          ]}>
            <Text style={[
              getTypographyStyle('heading1'),
              {
                textAlign: 'center',
                color: '#111827',
                ...getSpacingStyle('element')
              }
            ]}>What Our Entrepreneurs Say</Text>
            <Text style={[
              getTypographyStyle('subtitle'),
              {
                textAlign: 'center',
                color: '#6b7280',
                maxWidth: isMobile ? '100%' : 700,
                ...getSpacingStyle('component')
              }
            ]}>
              Don't just take our word for it. Here's what our entrepreneurs have to say about their business journey with Imuka Juniors.
            </Text>

            <View style={[
              styles.testimonialsGrid,
              {
                flexDirection: isMobile ? 'column' : 'row',
                justifyContent: 'space-between',
                gap: isMobile ? 20 : 24,
                flexWrap: 'wrap',
                width: '100%',
              }
            ]}>
              <View style={[
                styles.testimonialCard,
                {
                  backgroundColor: '#fff',
                  borderRadius: isMobile ? 16 : 20,
                  padding: isMobile ? 20 : 24,
                  flex: isMobile ? 0 : 1,
                  minWidth: isMobile ? '100%' : 300,
                  maxWidth: isMobile ? '100%' : 350,
                  shadowColor: '#000',
                  shadowOpacity: 0.12,
                  shadowRadius: 12,
                  elevation: 4,
                  borderWidth: 1,
                  borderColor: '#f1f3f4',
                }
              ]}>
                <View style={[
                  styles.testimonialStars,
                  { marginBottom: isMobile ? 12 : 16 }
                ]}>
                  <Text style={[
                    styles.starIcon,
                    { fontSize: isMobile ? 18 : 20 }
                  ]}>⭐⭐⭐⭐⭐</Text>
                </View>
                <Text style={[
                  getTypographyStyle('body'),
                  {
                    color: '#374151',
                    textAlign: 'left',
                    fontStyle: 'italic',
                    marginBottom: isMobile ? 16 : 20,
                    lineHeight: isMobile ? 24 : 28,
                  }
                ]}>
                  "Imuka Juniors boot camp completely transformed my business approach. The structured learning and practical strategies helped me launch my startup within 6 months!"
                </Text>
                <View style={[
                  styles.testimonialAuthor,
                  {
                    flexDirection: 'row',
                    alignItems: 'center',
                  }
                ]}>
                  <Image
                    source={{ uri: 'https://images.unsplash.com/photo-1511367461989-f85a21fda167?auto=format&fit=crop&w=400&q=80' }}
                    style={[
                      styles.authorAvatar,
                      {
                        width: isMobile ? 44 : 48,
                        height: isMobile ? 44 : 48,
                        borderRadius: isMobile ? 22 : 24,
                        marginRight: 12,
                      }
                    ]}
                  />
                  <View style={styles.authorInfo}>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#111827',
                        fontWeight: '600',
                        textAlign: 'left',
                        marginBottom: 4,
                      }
                    ]}>Sarah Johnson</Text>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#6b7280',
                        textAlign: 'left'
                      }
                    ]}>Founder, TechStartup</Text>
                  </View>
                </View>
              </View>

              <View style={[
                styles.testimonialCard,
                {
                  backgroundColor: '#fff',
                  borderRadius: isMobile ? 16 : 20,
                  padding: isMobile ? 20 : 24,
                  flex: isMobile ? 0 : 1,
                  minWidth: isMobile ? '100%' : 300,
                  maxWidth: isMobile ? '100%' : 350,
                  shadowColor: '#000',
                  shadowOpacity: 0.12,
                  shadowRadius: 12,
                  elevation: 4,
                  borderWidth: 1,
                  borderColor: '#f1f3f4',
                }
              ]}>
                <View style={[
                  styles.testimonialStars,
                  { marginBottom: isMobile ? 12 : 16 }
                ]}>
                  <Text style={[
                    styles.starIcon,
                    { fontSize: isMobile ? 18 : 20 }
                  ]}>⭐⭐⭐⭐⭐</Text>
                </View>
                <Text style={[
                  getTypographyStyle('body'),
                  {
                    color: '#374151',
                    textAlign: 'left',
                    fontStyle: 'italic',
                    marginBottom: isMobile ? 16 : 20,
                    lineHeight: isMobile ? 24 : 28,
                  }
                ]}>
                  "The Discover → Scale → Grow learning path is brilliant. I started with just an idea and now I'm running a successful e-commerce business. The mentors are amazing!"
                </Text>
                <View style={[
                  styles.testimonialAuthor,
                  {
                    flexDirection: 'row',
                    alignItems: 'center',
                  }
                ]}>
                  <Image
                    source={{ uri: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=crop&w=400&q=80' }}
                    style={[
                      styles.authorAvatar,
                      {
                        width: isMobile ? 44 : 48,
                        height: isMobile ? 44 : 48,
                        borderRadius: isMobile ? 22 : 24,
                        marginRight: 12,
                      }
                    ]}
                  />
                  <View style={styles.authorInfo}>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#111827',
                        fontWeight: '600',
                        textAlign: 'left',
                        marginBottom: 4,
                      }
                    ]}>Michael Chen</Text>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#6b7280',
                        textAlign: 'left'
                      }
                    ]}>CEO, E-commerce Empire</Text>
                  </View>
                </View>
              </View>

              <View style={[
                styles.testimonialCard,
                {
                  backgroundColor: '#fff',
                  borderRadius: isMobile ? 16 : 20,
                  padding: isMobile ? 20 : 24,
                  flex: isMobile ? 0 : 1,
                  minWidth: isMobile ? '100%' : 300,
                  maxWidth: isMobile ? '100%' : 350,
                  shadowColor: '#000',
                  shadowOpacity: 0.12,
                  shadowRadius: 12,
                  elevation: 4,
                  borderWidth: 1,
                  borderColor: '#f1f3f4',
                }
              ]}>
                <View style={[
                  styles.testimonialStars,
                  { marginBottom: isMobile ? 12 : 16 }
                ]}>
                  <Text style={[
                    styles.starIcon,
                    { fontSize: isMobile ? 18 : 20 }
                  ]}>⭐⭐⭐⭐⭐</Text>
                </View>
                <Text style={[
                  getTypographyStyle('body'),
                  {
                    color: '#374151',
                    textAlign: 'left',
                    fontStyle: 'italic',
                    marginBottom: isMobile ? 16 : 20,
                    lineHeight: isMobile ? 24 : 28,
                  }
                ]}>
                  "The practical business strategies and real-world case studies made all the difference. I went from having just an idea to running a profitable consulting business. Highly recommend!"
                </Text>
                <View style={[
                  styles.testimonialAuthor,
                  {
                    flexDirection: 'row',
                    alignItems: 'center',
                  }
                ]}>
                  <Image
                    source={{ uri: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=400&q=80' }}
                    style={[
                      styles.authorAvatar,
                      {
                        width: isMobile ? 44 : 48,
                        height: isMobile ? 44 : 48,
                        borderRadius: isMobile ? 22 : 24,
                        marginRight: 12,
                      }
                    ]}
                  />
                  <View style={styles.authorInfo}>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#111827',
                        fontWeight: '600',
                        textAlign: 'left',
                        marginBottom: 4,
                      }
                    ]}>Emily Rodriguez</Text>
                    <Text style={[
                      getTypographyStyle('caption'),
                      {
                        color: '#6b7280',
                        textAlign: 'left'
                      }
                    ]}>Founder, DigitalAgency</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </View>

        {/* Footer */}
        <View style={[
          styles.footer,
          getContainerStyle(),
          {
            backgroundColor: '#111827',
            borderRadius: 0,
            paddingVertical: isMobile ? 40 : 60,
            marginTop: isMobile ? 40 : 60,
          }
        ]}>
          <View style={[
            styles.footerContainer,
            {
              flexDirection: isMobile ? 'column' : 'row',
              justifyContent: 'space-between',
              marginBottom: isMobile ? 24 : 32,
              flexWrap: 'wrap',
              gap: isMobile ? 32 : 40,
            }
          ]}>
            <View style={[
              styles.footerSection,
              {
                flex: isMobile ? 0 : 1,
                minWidth: isMobile ? '100%' : 200,
                maxWidth: isMobile ? '100%' : 300,
              }
            ]}>
              <Text style={[
                getTypographyStyle('heading2'),
                {
                  color: '#fff',
                  textAlign: 'left',
                  marginBottom: isMobile ? 12 : 16,
                }
              ]}>Imuka Juniors</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  lineHeight: isMobile ? 24 : 28,
                }
              ]}>
                Empowering entrepreneurs with comprehensive boot camps and structured learning programs for business success.
              </Text>
            </View>

            <View style={[
              styles.footerSection,
              {
                flex: isMobile ? 0 : 1,
                minWidth: isMobile ? '100%' : 150,
              }
            ]}>
              <Text style={[
                getTypographyStyle('heading3'),
                {
                  color: '#fff',
                  textAlign: 'left',
                  marginBottom: isMobile ? 12 : 16,
                }
              ]}>Company</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>About Us</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Our Services</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Projects</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Contact</Text>
            </View>

            <View style={[
              styles.footerSection,
              {
                flex: isMobile ? 0 : 1,
                minWidth: isMobile ? '100%' : 150,
              }
            ]}>
              <Text style={[
                getTypographyStyle('heading3'),
                {
                  color: '#fff',
                  textAlign: 'left',
                  marginBottom: isMobile ? 12 : 16,
                }
              ]}>Learning Levels</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Discover</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Scale</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Grow</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>Boot Camps</Text>
            </View>

            <View style={[
              styles.footerSection,
              {
                flex: isMobile ? 0 : 1,
                minWidth: isMobile ? '100%' : 200,
              }
            ]}>
              <Text style={[
                getTypographyStyle('heading3'),
                {
                  color: '#fff',
                  textAlign: 'left',
                  marginBottom: isMobile ? 12 : 16,
                }
              ]}>Contact</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}><EMAIL></Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>+****************</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>123 Tech Street, Suite 200</Text>
              <Text style={[
                getTypographyStyle('body'),
                {
                  color: '#9ca3af',
                  textAlign: 'left',
                  marginBottom: 8,
                }
              ]}>San Francisco, CA 94105</Text>
            </View>
          </View>

          <View style={{
            width: '100%',
            paddingVertical: isMobile ? 20 : 24,
            borderTopWidth: 1,
            borderTopColor: '#374151',
            flexDirection: isMobile ? 'column' : 'row',
            alignItems: isMobile ? 'flex-start' : 'center',
            justifyContent: 'space-between',
            gap: isMobile ? 16 : 0,
            marginTop: isMobile ? 24 : 32,
          }}>
            <Text style={[
              getTypographyStyle('caption'),
              {
                color: '#9ca3af',
                textAlign: 'left'
              }
            ]}>© 2024 Imuka Juniors. All rights reserved.</Text>
            <View style={{
              flexDirection: 'row',
              gap: isMobile ? 20 : 24,
              alignItems: 'center',
            }}>
              <Text style={[
                getTypographyStyle('caption'),
                {
                  color: '#60a5fa',
                  fontWeight: '500'
                }
              ]}>LinkedIn</Text>
              <Text style={[
                getTypographyStyle('caption'),
                {
                  color: '#60a5fa',
                  fontWeight: '500'
                }
              ]}>Twitter</Text>
              <Text style={[
                getTypographyStyle('caption'),
                {
                  color: '#60a5fa',
                  fontWeight: '500'
                }
              ]}>Facebook</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </>
  );
}

const styles = StyleSheet.create({
  topBannerButtonText: {
    fontWeight: 'bold',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    alignItems: 'center',
  },
  topBanner: {
    backgroundColor: '#1976d2',
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  topBannerText: {
    color: '#fff',
    fontWeight: 'bold',
    letterSpacing: 0.2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  logo: {
    width: 36,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginRight: 20,
    letterSpacing: -0.5,
  },
  headerLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 20,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  searchInput: {
    flex: 1,
    height: 42,
    backgroundColor: '#f8f9fa',
    borderRadius: 21,
    paddingHorizontal: 18,
    fontSize: 16,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    fontWeight: '400',
  },
  headerNavLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 16,
    fontWeight: '600',
    letterSpacing: 0.1,
  },
  loginButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#1976d2',
    marginRight: 10,
  },
  loginText: {
    color: '#1976d2',
    fontWeight: '600',
    fontSize: 16,
  },
  signupButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#1976d2',
    marginRight: 12,
  },
  signupText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  heroSliderWrap: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
    backgroundColor: '#f8f9fa',
    paddingVertical: 40,
    marginTop: 24,
    marginBottom: 40,
  },
  heroArrowBtn: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    marginHorizontal: 12,
    zIndex: 2,
  },
  heroArrowText: {
    fontSize: 24,
    color: '#111827',
    fontWeight: 'bold',
  },
  heroSliderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'transparent',
    width: '100%',
    maxWidth: 900,
    borderRadius: 0,
    justifyContent: 'space-between',
    overflow: 'visible',
  },
  heroSliderCard: {
    backgroundColor: 'transparent',
    borderRadius: 0,
    padding: 32,
    marginRight: 40,
    minWidth: 320,
    maxWidth: 380,
  },
  heroSliderTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    lineHeight: 32,
    letterSpacing: -0.5,
  },
  heroSliderSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  heroSliderImageWrap: {
    position: 'relative',
    width: 360,
    height: 240,
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  heroSliderAccent: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: 240,
    height: 120,
    backgroundColor: '#1de9b6',
    borderTopLeftRadius: 32,
    borderBottomRightRadius: 20,
    zIndex: 1,
  },
  heroSliderImage: {
    width: 240,
    height: 240,
    borderRadius: 20,
    resizeMode: 'contain',
    zIndex: 2,
  },
  section: {
    marginTop: 24,
    paddingHorizontal: 32,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#222',
  },
  sectionSubtitle: {
    fontSize: 16,
    color: '#444',
    marginBottom: 16,
  },
  careerCardsRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 12,
    gap: 18,
  },
  careerCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginRight: 18,
    alignItems: 'center',
    width: 220,
    shadowColor: '#000',
    shadowOpacity: 0.07,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#eee',
  },
  careerCardImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginBottom: 10,
  },
  careerCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 8,
    textAlign: 'center',
  },
  careerCardStatsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    justifyContent: 'center',
  },
  careerCardStat: {
    fontSize: 14,
    color: '#666',
    marginRight: 8,
  },
  careerButton: {
    alignSelf: 'flex-start',
    backgroundColor: '#fff',
    borderColor: '#5624d0',
    borderWidth: 1,
    borderRadius: 4,
    paddingHorizontal: 14,
    paddingVertical: 8,
    marginTop: 6,
  },
  careerButtonText: {
    color: '#5624d0',
    fontWeight: 'bold',
    fontSize: 16,
  },
  categoriesScroll: {
    marginTop: 10,
    marginBottom: 8,
  },
  categoryBadge: {
    backgroundColor: '#f7f7f7',
    borderRadius: 16,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 12,
    borderWidth: 1,
    borderColor: '#eee',
  },
  categoryBadgeText: {
    color: '#5624d0',
    fontWeight: 'bold',
    fontSize: 16,
  },
  careerSectionTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 38,
    letterSpacing: -0.8,
  },
  careerSectionSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  careerCardsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 24,
    marginBottom: 16,
  },
  careerCardExact: {
    backgroundColor: '#fff',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 3,
    width: 320,
    marginHorizontal: 8,
    paddingBottom: 20,
    alignItems: 'flex-start',
  },
  careerCardImageWrap: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'flex-start',
    padding: 20,
    position: 'relative',
  },
  careerCardIcon: {
    width: 56,
    height: 56,
    borderRadius: 12,
    marginRight: 12,
  },
  careerCardAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 4,
    borderColor: '#fff',
    position: 'absolute',
    left: 80,
    bottom: 12,
  },
  careerCardExactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginLeft: 20,
    lineHeight: 24,
    letterSpacing: -0.3,
  },
  careerCardBadgesRow: {
    flexDirection: 'row',
    marginTop: 12,
    marginLeft: 20,
    gap: 8,
  },
  careerBadge: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#1976d2',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 4,
    marginRight: 6,
  },
  careerBadgeText: {
    color: '#6b7280',
    fontWeight: '600',
    fontSize: 14,
    letterSpacing: 0.2,
  },
  careerExactButton: {
    alignSelf: 'center',
    backgroundColor: '#fff',
    borderColor: '#1976d2',
    borderWidth: 2,
    borderRadius: 8,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginTop: 12,
  },
  careerExactButtonText: {
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.3,
  },
  skillsSection: {
    marginTop: 48,
    paddingHorizontal: 32,
    backgroundColor: '#f8f9fa',
    paddingVertical: 40,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  skillsSectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    textAlign: 'center',
    lineHeight: 32,
    letterSpacing: -0.6,
  },
  skillsSectionSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  categoryTabsScroll: {
    marginBottom: 24,
  },
  categoryTabsContent: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    flexDirection: 'row',
  },
  categoryTab: {
    paddingHorizontal: 24,
    paddingVertical: 12,
    marginHorizontal: 12,
    borderRadius: 24,
  },
  categoryTabSelected: {
    borderBottomWidth: 3,
    borderBottomColor: '#1976d2',
  },
  categoryTabText: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  categoryTabTextSelected: {
    color: '#1976d2',
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
  topicsScroll: {
    marginBottom: 32,
  },
  topicBubble: {
    backgroundColor: '#fff',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  topicBubbleSelected: {
    backgroundColor: '#4B4B63',
    borderColor: '#4B4B63',
  },
  topicBubbleText: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  topicBubbleTextSelected: {
    color: '#fff',
  },
  topicBubbleLearners: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 4,
    fontWeight: '400',
    letterSpacing: 0.1,
  },
  topicBubbleLearnersSelected: {
    color: '#d1d5db',
  },
  coursesScroll: {
    marginBottom: 24,
  },
  courseCard: {
    width: 300,
    backgroundColor: '#fff',
    borderRadius: 12,
    marginRight: 20,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    overflow: 'hidden',
  },
  courseCardImage: {
    width: '100%',
    height: 160,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  courseCardContent: {
    padding: 16,
  },
  courseCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    lineHeight: 22,
    letterSpacing: -0.2,
  },
  courseCardInstructors: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
    fontWeight: '400',
    letterSpacing: 0.1,
  },
  courseCardRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  courseCardRatingText: {
    fontSize: 13,
    color: '#6b7280',
    marginRight: 6,
  },
  courseCardRatingCount: {
    fontSize: 13,
    color: '#9ca3af',
  },
  courseCardPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    letterSpacing: -0.2,
  },
  courseCardBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
  },
  badgeBestseller: {
    backgroundColor: '#1de9b6',
  },
  badgePremium: {
    backgroundColor: '#1976d2',
  },
  courseCardBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
    letterSpacing: 0.3,
  },
  showAllButton: {
    alignSelf: 'center',
    backgroundColor: '#fff',
    borderColor: '#1976d2',
    borderWidth: 2,
    borderRadius: 8,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  showAllButtonText: {
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.3,
  },
  slideIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  slideIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#e0e0e0',
    marginHorizontal: 5,
  },
  slideIndicatorActive: {
    backgroundColor: '#1976d2',
  },
  aboutSection: {
    marginTop: 48,
    paddingHorizontal: 32,
    backgroundColor: '#fff',
    paddingVertical: 60,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  aboutContainer: {
    alignItems: 'center',
    maxWidth: 800,
    alignSelf: 'center',
  },
  trustSection: {
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 10,
    maxWidth: 200,
  },
  trustTitle: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 8,
  },
  trustLogos: {
    flexDirection: 'row',
    gap: 16,
  },
  trustLogo: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#6b7280',
  },
  aboutGraphicContainer: {
    position: 'relative',
    width: 400,
    height: 400,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 60,
  },
  mainCircle: {
    width: 300,
    height: 300,
    borderRadius: 150,
    backgroundColor: '#ff6b35',
    position: 'absolute',
    top: 0,
    left: 0,
    zIndex: 1,
  },
  mainCircleImage: {
    width: 200,
    height: 200,
    position: 'absolute',
    top: 50,
    left: 50,
  },
  avatar1: {
    position: 'absolute',
    top: 100,
    left: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatar2: {
    position: 'absolute',
    top: 200,
    right: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatar3: {
    position: 'absolute',
    bottom: 100,
    left: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatar4: {
    position: 'absolute',
    bottom: 200,
    right: -50,
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#ff6b35',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  aboutTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 24,
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  aboutContent: {
    flex: 1,
    paddingLeft: 32,
    marginTop: 60,
    maxWidth: 500,
  },
  aboutSubtitle: {
    fontSize: 12,
    color: '#ff6b35',
    marginBottom: 8,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    borderBottomWidth: 2,
    borderBottomColor: '#ff6b35',
    alignSelf: 'flex-start',
    paddingBottom: 4,
  },
  aboutMainTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    lineHeight: 38,
  },
  aboutMission: {
    fontSize: 18,
    color: '#111827',
    marginBottom: 32,
    lineHeight: 28,
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  aboutBelieveTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 12,
    lineHeight: 30,
  },
  aboutBelieveText: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 16,
    lineHeight: 24,
  },
  aboutBullets: {
    marginBottom: 24,
  },
  aboutBullet: {
    fontSize: 16,
    color: '#6b7280',
    marginBottom: 8,
    lineHeight: 24,
  },
  aboutStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 24,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statNumber: {
    fontSize: 40,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  statLabel: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
    textAlign: 'center',
    letterSpacing: 0.2,
  },
  statMission: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  learnMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ff6b35',
    borderRadius: 6,
    paddingHorizontal: 20,
    paddingVertical: 10,
    alignSelf: 'flex-start',
  },
  learnMoreText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  learnMoreArrow: {
    fontSize: 16,
    color: '#fff',
    marginLeft: 8,
  },
  dottedLine1: {
    position: 'absolute',
    top: 100,
    left: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dottedLine2: {
    position: 'absolute',
    top: 200,
    right: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dottedLine3: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  dottedLine4: {
    position: 'absolute',
    bottom: 200,
    right: 0,
    width: 100,
    height: 1,
    backgroundColor: 'transparent',
    borderStyle: 'dotted',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 32,
    paddingHorizontal: 20,
  },
  testimonialsSection: {
    paddingVertical: 60,
    paddingHorizontal: 32,
    backgroundColor: '#f8f9fa',
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  testimonialsContainer: {
    alignItems: 'center',
    maxWidth: 1000,
    alignSelf: 'center',
  },
  testimonialsTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    textAlign: 'center',
    letterSpacing: -0.6,
  },
  testimonialsSubtitle: {
    fontSize: 20,
    color: '#6b7280',
    marginBottom: 48,
    textAlign: 'center',
    lineHeight: 28,
    maxWidth: 600,
    fontWeight: '400',
    letterSpacing: 0.3,
  },
  testimonialsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 24,
    flexWrap: 'wrap',
  },
  testimonialCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 24,
    flex: 1,
    minWidth: 300,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  testimonialStars: {
    marginBottom: 16,
  },
  starIcon: {
    fontSize: 20,
  },
  testimonialText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 26,
    marginBottom: 20,
    fontStyle: 'italic',
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  testimonialAuthor: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    marginRight: 12,
  },
  authorInfo: {
    flex: 1,
  },
  authorName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
    letterSpacing: -0.2,
  },
  authorRole: {
    fontSize: 14,
    color: '#6b7280',
    fontWeight: '500',
    letterSpacing: 0.1,
  },
  footer: {
    backgroundColor: '#111827',
    paddingVertical: 48,
    paddingHorizontal: 32,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    flexWrap: 'wrap',
    gap: 32,
  },
  footerSection: {
    flex: 1,
    minWidth: 200,
  },
  footerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
    letterSpacing: -0.3,
  },
  footerDescription: {
    fontSize: 16,
    color: '#9ca3af',
    lineHeight: 24,
    marginBottom: 16,
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  footerSectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 16,
    letterSpacing: -0.2,
  },
  footerLink: {
    fontSize: 16,
    color: '#9ca3af',
    marginBottom: 8,
    lineHeight: 24,
    fontWeight: '400',
    letterSpacing: 0.1,
  },
  footerBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 24,
    borderTopWidth: 1,
    borderTopColor: '#374151',
    flexWrap: 'wrap',
    gap: 16,
  },
  footerCopyright: {
    fontSize: 14,
    color: '#9ca3af',
    fontWeight: '400',
    letterSpacing: 0.2,
  },
  footerSocial: {
    flexDirection: 'row',
    gap: 24,
  },
  socialLink: {
    fontSize: 14,
    color: '#9ca3af',
    fontWeight: '500',
    letterSpacing: 0.2,
  },
});
