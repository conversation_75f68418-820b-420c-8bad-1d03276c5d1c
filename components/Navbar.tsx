import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Dimensions, Image, StyleSheet, Text, TextInput, TouchableOpacity, View } from 'react-native';

interface NavbarProps {
  showLogin: boolean;
  showSignup: boolean;
  setShowLogin: (open: boolean) => void;
  setShowSignup: (open: boolean) => void;
}

export default function Navbar({ showLogin, showSignup, setShowLogin, setShowSignup }: NavbarProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const windowWidth = Dimensions.get('window').width;
  const isMobile = windowWidth < 700;
  const isTablet = windowWidth >= 700 && windowWidth < 1000;

  return (
    <>
      <View style={[styles.header, { flexDirection: isMobile ? 'row' : 'row', alignItems: 'center', paddingHorizontal: isMobile ? 12 : isTablet ? 24 : 32, maxWidth: isMobile ? '100%' : 1200, width: '100%' }]}> 
        <Image source={require('@/assets/images/favicon.png')} style={[styles.logo, { width: isMobile ? 32 : 36, height: isMobile ? 32 : 36 }]} />
        <Text style={[styles.headerTitle, isMobile && { fontSize: 24, lineHeight: 28 }]}>Imuka Juniors</Text>
        {isMobile ? (
          <TouchableOpacity style={{ marginLeft: 'auto' }} onPress={() => setMobileMenuOpen(true)}>
            <Ionicons name="menu" size={32} color="#111827" />
          </TouchableOpacity>
        ) : (
          <>
            <TouchableOpacity><Text style={styles.headerLink}>Explore</Text></TouchableOpacity>
            <TextInput style={styles.searchInput} placeholder="Learn anything: Idea generation, development & more." placeholderTextColor="#888" />
            <TouchableOpacity><Text style={styles.headerNavLink}>Boot Camps</Text></TouchableOpacity>
            <TouchableOpacity><Text style={styles.headerNavLink}>Business Skills</Text></TouchableOpacity>
            <TouchableOpacity style={styles.loginButton} onPress={() => setShowLogin(true)}><Text style={styles.loginText}>Log in</Text></TouchableOpacity>
            <TouchableOpacity style={styles.signupButton} onPress={() => setShowSignup(true)}><Text style={styles.signupText}>Sign up</Text></TouchableOpacity>
          </>
        )}
      </View>
      {isMobile && mobileMenuOpen && (
        <View style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', backgroundColor: 'rgba(17,24,39,0.97)', zIndex: 100, paddingTop: 0, paddingHorizontal: 0, justifyContent: 'flex-start', alignItems: 'flex-start' }}>
          <TouchableOpacity style={{ position: 'absolute', top: 18, right: 18, zIndex: 101 }} onPress={() => setMobileMenuOpen(false)}>
            <Ionicons name="close" size={36} color="#fff" />
          </TouchableOpacity>
          <View style={{ marginTop: 64, width: '100%', paddingHorizontal: 24 }}>
            <TouchableOpacity onPress={() => setMobileMenuOpen(false)}>
              <Text style={{ color: '#fff', fontSize: 22, fontWeight: 'bold', marginBottom: 28, textAlign: 'left' }}>Explore</Text>
            </TouchableOpacity>
            <TextInput style={{ backgroundColor: '#fff', color: '#111827', borderRadius: 8, paddingHorizontal: 16, paddingVertical: 10, fontSize: 16, marginBottom: 28, width: '100%' }} placeholder="Learn anything: Idea generation, development & more." placeholderTextColor="#888" />
            <TouchableOpacity onPress={() => setMobileMenuOpen(false)}>
              <Text style={{ color: '#fff', fontSize: 20, marginBottom: 24, textAlign: 'left' }}>Boot Camps</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setMobileMenuOpen(false)}>
              <Text style={{ color: '#fff', fontSize: 20, marginBottom: 24, textAlign: 'left' }}>Business Skills</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ backgroundColor: '#fff', borderRadius: 6, paddingVertical: 10, marginBottom: 16, alignItems: 'center' }} onPress={() => setShowLogin(true)}>
              <Text style={{ color: '#1976d2', fontWeight: 'bold', fontSize: 16 }}>Log in</Text>
            </TouchableOpacity>
            <TouchableOpacity style={{ backgroundColor: '#1976d2', borderRadius: 6, paddingVertical: 10, alignItems: 'center' }} onPress={() => setShowSignup(true)}>
              <Text style={{ color: '#fff', fontWeight: 'bold', fontSize: 16 }}>Sign up</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
    width: '100%',
    alignSelf: 'center',
    zIndex: 10,
  },
  logo: {
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    flex: 1,
  },
  headerLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 20,
  },
  headerNavLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 20,
  },
  searchInput: {
    flex: 1,
    backgroundColor: '#f0f2f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    color: '#111827',
  },
  loginButton: {
    backgroundColor: '#1976d2',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    alignSelf: 'center',
  },
  loginText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  signupButton: {
    backgroundColor: '#fff',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#1976d2',
    alignSelf: 'center',
  },
  signupText: {
    color: '#1976d2',
    fontSize: 16,
    fontWeight: 'bold',
  },
}); 