import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, Switch, TextInput, TextStyle, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface SignupModalProps {
  open: boolean;
  onClose: () => void;
}

export default function SignupModal({ open, onClose }: SignupModalProps) {
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [hasBusiness, setHasBusiness] = useState(false);
  const [businessName, setBusinessName] = useState('');
  const [businessType, setBusinessType] = useState('');
  const [businessDescription, setBusinessDescription] = useState('');
  const [businessEmail, setBusinessEmail] = useState('');
  const [businessPhone, setBusinessPhone] = useState('');
  const [businessWebsite, setBusinessWebsite] = useState('');
  const [businessAddress, setBusinessAddress] = useState('');
  const [businessRegNumber, setBusinessRegNumber] = useState('');

  if (!open) return null;

  const titleTextStyle: TextStyle = { marginBottom: 18, color: '#222', textAlign: 'center', fontWeight: 'bold', fontSize: 22 };

  // Web overlay
  if (Platform.OS === 'web') {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'rgba(0,0,0,0.4)',
        zIndex: 1000,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <div style={{
          background: '#fff',
          borderRadius: 12,
          padding: 0,
          minWidth: 500,
          maxWidth: 500,
          alignItems: 'center',
          boxShadow: '0 4px 32px rgba(0,0,0,0.15)',
          display: 'flex',
          flexDirection: 'column',
          maxHeight: '90vh',
        }}>
          <div style={{ width: '100%', maxHeight: '90vh', overflowY: 'auto', padding: 20, boxSizing: 'border-box', display: 'flex', flexDirection: 'column', alignItems: 'center', margin: '0 auto', scrollbarWidth: 'none', msOverflowStyle: 'none' }}>
            <style>{`div[role='dialog'] > div > div::-webkit-scrollbar { display: none !important; }`}</style>
            <div style={{ marginBottom: 18, textAlign: 'center' }}>
              <ThemedText type="title" style={titleTextStyle}>Sign Up</ThemedText>
              <div style={{ width: '100%' }}>
                <ThemedText style={{ color: '#888', fontSize: 15, marginTop: 8, textAlign: 'center' }}>Please fill in all required fields to create your account.</ThemedText>
              </div>
            </div>
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Email" value={email} onChangeText={setEmail} autoCapitalize="none" keyboardType="email-address" />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Phone" value={phone} onChangeText={setPhone} keyboardType="phone-pad" />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Full Name" value={name} onChangeText={setName} />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Username" value={username} onChangeText={setUsername} autoCapitalize="none" />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Password" value={password} onChangeText={setPassword} secureTextEntry />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Confirm Password" value={confirmPassword} onChangeText={setConfirmPassword} secureTextEntry />
            <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', marginTop: 12, marginBottom: 8 }}>
              <input type="checkbox" checked={hasBusiness} onChange={e => setHasBusiness(e.target.checked)} style={{ marginRight: 8 }} />
              <ThemedText style={{ color: '#222', marginLeft: 4 }}>Do you have a business?</ThemedText>
            </div>
            <ThemedText style={{ color: '#888', fontSize: 13, marginBottom: 8, textAlign: 'center' }}>
              This will be required if you would love to access Imuka Business Manager Services.
            </ThemedText>
            {hasBusiness && (
              <>
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Name" value={businessName} onChangeText={setBusinessName} />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Type" value={businessType} onChangeText={setBusinessType} />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Email" value={businessEmail} onChangeText={setBusinessEmail} autoCapitalize="none" keyboardType="email-address" />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Phone" value={businessPhone} onChangeText={setBusinessPhone} keyboardType="phone-pad" />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Website" value={businessWebsite} onChangeText={setBusinessWebsite} autoCapitalize="none" />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Address" value={businessAddress} onChangeText={setBusinessAddress} />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Registration Number" value={businessRegNumber} onChangeText={setBusinessRegNumber} />
                <View style={{ width: 440, alignSelf: 'center', marginTop: 8 }}>
                  <ThemedText style={{ marginBottom: 4, color: '#222', fontSize: 15 }}>Business Description</ThemedText>
                  <TextInput
                    style={[styles.input, { minHeight: 90, paddingTop: 12, textAlignVertical: 'top', width: 440 }]}
                    placeholder="Describe your business..."
                    value={businessDescription}
                    onChangeText={text => {
                      if (text.length <= 200) setBusinessDescription(text);
                    }}
                    multiline
                    maxLength={200}
                  />
                  <ThemedText style={{ color: '#888', fontSize: 13, marginTop: 2, textAlign: 'right' }}>{businessDescription.length}/200</ThemedText>
                </View>
              </>
            )}
            <TouchableOpacity style={styles.button}>
              <ThemedText style={styles.buttonText}>Sign Up</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <ThemedText type="link">Close</ThemedText>
            </TouchableOpacity>
          </div>
        </div>
      </div>
    );
  }

  // Native overlay
  return (
    <View style={styles.nativeOverlay}>
      <ThemedView style={[styles.nativeModal, { minWidth: 500, maxWidth: 500 }]}>
        <ScrollView contentContainerStyle={{ alignItems: 'center', padding: 20, justifyContent: 'center' }} style={{ width: '100%' }}>
          <View style={{ marginBottom: 18, alignItems: 'center', width: '100%' }}>
            <ThemedText type="title" style={titleTextStyle}>Sign Up</ThemedText>
            <View style={{ width: '100%' }}>
              <ThemedText style={{ color: '#888', fontSize: 15, marginTop: 8, textAlign: 'center' }}>Please fill in all required fields to create your account.</ThemedText>
            </View>
          </View>
          <View style={{ width: '100%', alignItems: 'center' }}>
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Email" value={email} onChangeText={setEmail} autoCapitalize="none" keyboardType="email-address" />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Phone" value={phone} onChangeText={setPhone} keyboardType="phone-pad" />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Full Name" value={name} onChangeText={setName} />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Username" value={username} onChangeText={setUsername} autoCapitalize="none" />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Password" value={password} onChangeText={setPassword} secureTextEntry />
            <TextInput style={[styles.input, { width: 440 }]} placeholder="Confirm Password" value={confirmPassword} onChangeText={setConfirmPassword} secureTextEntry />
            <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 12, marginBottom: 8 }}>
              <Switch value={hasBusiness} onValueChange={setHasBusiness} />
              <ThemedText style={{ marginLeft: 8, color: '#222' }}>Do you have a business?</ThemedText>
            </View>
            <ThemedText style={{ color: '#888', fontSize: 13, marginBottom: 8, textAlign: 'center' }}>
              This will be required if you would love to access Imuka Business Manager Services.
            </ThemedText>
            {hasBusiness && (
              <>
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Name" value={businessName} onChangeText={setBusinessName} />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Type" value={businessType} onChangeText={setBusinessType} />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Email" value={businessEmail} onChangeText={setBusinessEmail} autoCapitalize="none" keyboardType="email-address" />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Phone" value={businessPhone} onChangeText={setBusinessPhone} keyboardType="phone-pad" />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Website" value={businessWebsite} onChangeText={setBusinessWebsite} autoCapitalize="none" />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Address" value={businessAddress} onChangeText={setBusinessAddress} />
                <TextInput style={[styles.input, { width: 440 }]} placeholder="Business Registration Number" value={businessRegNumber} onChangeText={setBusinessRegNumber} />
                <View style={{ width: 440, alignSelf: 'center', marginTop: 8 }}>
                  <ThemedText style={{ marginBottom: 4, color: '#222', fontSize: 15 }}>Business Description</ThemedText>
                  <TextInput
                    style={[styles.input, { minHeight: 90, paddingTop: 12, textAlignVertical: 'top', width: 440 }]}
                    placeholder="Describe your business..."
                    value={businessDescription}
                    onChangeText={text => {
                      if (text.length <= 200) setBusinessDescription(text);
                    }}
                    multiline
                    maxLength={200}
                  />
                  <ThemedText style={{ color: '#888', fontSize: 13, marginTop: 2, textAlign: 'right' }}>{businessDescription.length}/200</ThemedText>
                </View>
              </>
            )}
            <TouchableOpacity style={styles.button}>
              <ThemedText style={styles.buttonText}>Sign Up</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <ThemedText type="link">Close</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </View>
  );
}

const styles = StyleSheet.create({
  nativeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nativeModal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 0,
    minWidth: 320,
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
    maxHeight: '90vh',
    width: '100%',
  },
  input: {
    width: 240,
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#ccc',
    backgroundColor: '#fff',
  },
  button: {
    width: 240,
    backgroundColor: '#007AFF',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  closeButton: {
    marginTop: 24,
    alignSelf: 'center',
  },
}); 