import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { ResizeMode, Video } from 'expo-av';
import React, { useEffect, useRef, useState } from 'react';
import { Platform, StyleProp, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from 'react-native';

function formatTime(seconds: number) {
  const m = Math.floor(seconds / 60).toString().padStart(2, '0');
  const s = Math.floor(seconds % 60).toString().padStart(2, '0');
  return `${m}:${s}`;
}

interface VideoPlayerProps {
  uri?: string;
  height?: number;
  style?: StyleProp<ViewStyle>;
  videoStyle?: StyleProp<ViewStyle>;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  uri = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
  height = 500,
  style,
  videoStyle
}) => {
  console.log('VideoPlayer: Initializing with URI:', uri);
  console.log('VideoPlayer: Platform:', Platform.OS);

  // --- WEB: Use native HTML5 video ---
  if (Platform.OS === 'web') {
    const [isPlaying, setIsPlaying] = useState(false);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [volume, setVolume] = useState(1);
    const [isMuted, setIsMuted] = useState(false);
    const [showControls, setShowControls] = useState(true);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    
    const videoRef = useRef<HTMLVideoElement>(null);
    const controlsTimeoutRef = useRef<NodeJS.Timeout>();

    const handlePlay = () => {
      if (videoRef.current) {
        if (isPlaying) {
          videoRef.current.pause();
        } else {
          videoRef.current.play();
        }
      }
    };

    const handleSeek = (value: number) => {
      if (videoRef.current && duration) {
        videoRef.current.currentTime = value * duration;
      }
    };

    const handleVolumeChange = (value: number) => {
      setVolume(value);
      if (videoRef.current) {
        videoRef.current.volume = value;
      }
    };

    const toggleMute = () => {
      if (videoRef.current) {
        videoRef.current.muted = !isMuted;
        setIsMuted(!isMuted);
      }
    };

    const handleFullscreen = () => {
      if (videoRef.current) {
        if (videoRef.current.requestFullscreen) {
          videoRef.current.requestFullscreen();
        }
      }
    };

    const showControlsTemporarily = () => {
      setShowControls(true);
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    };

    useEffect(() => {
      return () => {
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current);
        }
      };
    }, []);

    return (
      <div 
        style={{ 
          width: '100%', 
          height: height,
          backgroundColor: '#000', 
          borderRadius: 8,
          overflow: 'hidden',
          position: 'relative',
          cursor: showControls ? 'default' : 'none',
          ...(style as any || {})
        }}
        onMouseMove={showControlsTemporarily}
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => setShowControls(false)}
      >
        <video
          ref={videoRef}
          src={uri}
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain',
            ...(videoStyle as any || {})
          }}
          onLoadStart={() => {
            console.log('HTML5 Video: Load started');
            setIsLoading(true);
            setError(null);
          }}
          onLoadedMetadata={() => {
            console.log('HTML5 Video: Metadata loaded');
            if (videoRef.current) {
              setDuration(videoRef.current.duration);
            }
          }}
          onCanPlay={() => {
            console.log('HTML5 Video: Can play');
            setIsLoading(false);
          }}
          onPlay={() => {
            console.log('HTML5 Video: Playing');
            setIsPlaying(true);
          }}
          onPause={() => {
            console.log('HTML5 Video: Paused');
            setIsPlaying(false);
          }}
          onTimeUpdate={() => {
            if (videoRef.current) {
              setCurrentTime(videoRef.current.currentTime);
            }
          }}
          onError={(e) => {
            console.error('HTML5 Video: Error', e);
            setError('Failed to load video');
            setIsLoading(false);
          }}
          onEnded={() => {
            console.log('HTML5 Video: Ended');
            setIsPlaying(false);
          }}
        />

        {/* Loading overlay */}
        {isLoading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0,0,0,0.7)',
            color: '#fff',
            fontSize: 16
          }}>
            Loading video...
          </div>
        )}

        {/* Error overlay */}
        {error && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0,0,0,0.8)',
            color: '#fff',
            textAlign: 'center'
          }}>
            <div style={{ fontSize: 16, marginBottom: 16 }}>{error}</div>
            <button 
              onClick={() => {
                setError(null);
                setIsLoading(true);
                if (videoRef.current) {
                  videoRef.current.load();
                }
              }}
              style={{
                backgroundColor: '#ff0000',
                color: '#fff',
                border: 'none',
                padding: '10px 20px',
                borderRadius: 6,
                cursor: 'pointer',
                fontSize: 16,
                fontWeight: 'bold'
              }}
            >
              Retry
            </button>
          </div>
        )}

        {/* Custom controls */}
        {showControls && !error && !isLoading && (
          <div style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            background: 'linear-gradient(transparent, rgba(0,0,0,0.8))',
            padding: '20px 16px 16px',
            color: '#fff'
          }}>
            {/* Progress bar */}
            <div style={{
              width: '100%',
              height: 4,
              backgroundColor: 'rgba(255,255,255,0.3)',
              borderRadius: 2,
              marginBottom: 12,
              cursor: 'pointer'
            }}
            onClick={(e) => {
              const rect = e.currentTarget.getBoundingClientRect();
              const percent = (e.clientX - rect.left) / rect.width;
              handleSeek(percent);
            }}>
              <div style={{
                width: `${duration ? (currentTime / duration) * 100 : 0}%`,
                height: '100%',
                backgroundColor: '#ff0000',
                borderRadius: 2
              }} />
            </div>

            {/* Controls row */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <button
                  onClick={handlePlay}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#fff',
                    cursor: 'pointer',
                    fontSize: 24,
                    padding: 8
                  }}
                >
                  {isPlaying ? '⏸️' : '▶️'}
                </button>
                
                <span style={{ fontSize: 14, minWidth: 45 }}>
                  {formatTime(currentTime)}
                </span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <span style={{ fontSize: 14, minWidth: 45 }}>
                  {formatTime(duration)}
                </span>

                <button
                  onClick={toggleMute}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#fff',
                    cursor: 'pointer',
                    fontSize: 20,
                    padding: 8
                  }}
                >
                  {isMuted ? '🔇' : '🔊'}
                </button>

                <button
                  onClick={handleFullscreen}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#fff',
                    cursor: 'pointer',
                    fontSize: 20,
                    padding: 8
                  }}
                >
                  ⛶
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Debug info */}
        <div style={{
          position: 'absolute',
          top: 10,
          left: 10,
          right: 10,
          backgroundColor: 'rgba(0,0,0,0.8)',
          padding: 8,
          borderRadius: 4,
          color: '#fff',
          fontSize: 10,
          fontFamily: 'monospace'
        }}>
          <div>Platform: Web (HTML5 Video)</div>
          <div>URI: {uri}</div>
          <div>Status: {isLoading ? 'Loading' : error ? 'Error' : 'Ready'}</div>
          <div>Playing: {isPlaying ? 'Yes' : 'No'}</div>
          <div>Duration: {formatTime(duration)}</div>
        </div>
      </div>
    );
  }

  // --- NATIVE: Keep existing expo-av implementation ---
  const videoRef = useRef<Video>(null);
  const [status, setStatus] = useState<any>({});
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isBuffering, setIsBuffering] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const controlsTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);

  const handleShowControls = () => {
    setShowControls(true);
    if (controlsTimeout.current) clearTimeout(controlsTimeout.current);
    controlsTimeout.current = setTimeout(() => {
      setShowControls(false);
    }, 3000);
  };

  const handlePlayPause = () => {
    if (status.isPlaying) {
      videoRef.current?.pauseAsync();
    } else {
      videoRef.current?.playAsync();
    }
  };

  const handleMute = () => {
    setIsMuted(!isMuted);
    videoRef.current?.setIsMutedAsync(!isMuted);
  };

  const handleSeek = (value: number) => {
    if (status.durationMillis) {
      videoRef.current?.setPositionAsync(value * status.durationMillis);
    }
  };

  const handleFullscreen = () => {
    videoRef.current?.presentFullscreenPlayer();
  };

  return (
    <View style={[styles.container, { height }, style]}>
      <TouchableOpacity 
        style={styles.videoContainer} 
        activeOpacity={1}
        onPress={handleShowControls}
      >
        <Video
          ref={videoRef}
          source={{ uri }}
          style={[styles.video, { height }, videoStyle]}
          resizeMode={ResizeMode.CONTAIN}
          onPlaybackStatusUpdate={(status) => {
            setStatus(status);
            if (status.isLoaded) {
              setIsBuffering(status.isBuffering || false);
              setError(null);
            } else if (status.error) {
              setError(`Playback error: ${status.error}`);
            }
          }}
          onError={(error) => {
            setError('Failed to load video');
            setIsBuffering(false);
          }}
          onLoad={() => {
            setIsBuffering(false);
            setError(null);
          }}
          isMuted={isMuted}
          shouldPlay={false}
          useNativeControls={false}
        />
        
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity 
              style={styles.retryButton}
              onPress={() => {
                setError(null);
                setIsBuffering(true);
                videoRef.current?.loadAsync({ uri }, {}, false);
              }}
            >
              <Text style={styles.retryText}>Retry</Text>
            </TouchableOpacity>
          </View>
        )}
        
        {isBuffering && !error && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Loading...</Text>
          </View>
        )}

        {showControls && !error && !isBuffering && (
          <View style={styles.controlsOverlay}>
            <TouchableOpacity 
              style={styles.centerPlayButton}
              onPress={handlePlayPause}
            >
              <Ionicons 
                name={status.isPlaying ? 'pause' : 'play'} 
                size={60} 
                color="rgba(255,255,255,0.9)" 
              />
            </TouchableOpacity>

            <View style={styles.bottomControls}>
              <View style={styles.progressContainer}>
                <Slider
                  style={styles.progressSlider}
                  minimumValue={0}
                  maximumValue={1}
                  value={status.positionMillis && status.durationMillis ? 
                    status.positionMillis / status.durationMillis : 0}
                  onValueChange={handleSeek}
                  minimumTrackTintColor="#ff0000"
                  maximumTrackTintColor="rgba(255,255,255,0.3)"
                  thumbTintColor="#ff0000"
                />
              </View>
              
              <View style={styles.controlsRow}>
                <TouchableOpacity onPress={handlePlayPause} style={styles.controlButton}>
                  <Ionicons 
                    name={status.isPlaying ? 'pause' : 'play'} 
                    size={24} 
                    color="#fff" 
                  />
                </TouchableOpacity>
                
                <Text style={styles.timeText}>
                  {formatTime((status.positionMillis || 0) / 1000)}
                </Text>
                
                <View style={styles.spacer} />
                
                <Text style={styles.timeText}>
                  {formatTime((status.durationMillis || 0) / 1000)}
                </Text>
                
                <TouchableOpacity onPress={handleMute} style={styles.controlButton}>
                  <Ionicons 
                    name={isMuted ? 'volume-mute' : 'volume-high'} 
                    size={24} 
                    color="#fff" 
                  />
                </TouchableOpacity>
                
                <TouchableOpacity onPress={handleFullscreen} style={styles.controlButton}>
                  <Ionicons name="expand" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#000',
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  videoContainer: {
    flex: 1,
    position: 'relative',
  },
  video: {
    width: '100%',
    backgroundColor: '#000',
  },
  loadingContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.7)',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
  },
  errorContainer: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.8)',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    marginBottom: 16,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#ff0000',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 6,
  },
  retryText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  controlsOverlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  centerPlayButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  bottomControls: {
    width: '100%',
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressSlider: {
    width: '100%',
    height: 20,
  },
  controlsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  controlButton: {
    padding: 8,
  },
  timeText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
    minWidth: 45,
    textAlign: 'center',
  },
  spacer: {
    flex: 1,
  },
});

export default VideoPlayer; 
