import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { Image, StyleSheet, Text, TextInput, TouchableOpacity, useWindowDimensions, View } from 'react-native';

interface MainNavbarProps {
  onShowLogin: () => void;
  onShowSignup: () => void;
}

export default function MainNavbar({ onShowLogin, onShowSignup }: MainNavbarProps) {
  const { width: windowWidth } = useWindowDimensions();
  const isMobile = windowWidth < 700;
  const isTablet = windowWidth >= 700 && windowWidth < 1000;
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <>
      <View style={[
        styles.header,
        {
          flexDirection: isMobile ? 'row' : 'row',
          alignItems: 'center',
          paddingHorizontal: isMobile ? 12 : isTablet ? 24 : 32,
          maxWidth: isMobile ? '100%' : 1200,
          width: '100%',
        },
      ]}>
        <Image source={require('@/assets/images/favicon.png')} style={[styles.logo, { width: isMobile ? 32 : 36, height: isMobile ? 32 : 36 }]} />
        <Text style={[styles.headerTitle, isMobile && { fontSize: 24, lineHeight: 28 }]}>Imuka Juniors</Text>
        {isMobile ? (
          <TouchableOpacity style={{ marginLeft: 'auto' }} onPress={() => setMobileMenuOpen(true)}>
            <Ionicons name="menu" size={32} color="#111827" />
          </TouchableOpacity>
        ) : (
          <>
            <TouchableOpacity><Text style={styles.headerLink}>Explore</Text></TouchableOpacity>
            <TextInput style={styles.searchInput} placeholder="Learn anything: Idea generation, development & more." placeholderTextColor="#888" />
            <TouchableOpacity><Text style={styles.headerNavLink}>Boot Camps</Text></TouchableOpacity>
            <TouchableOpacity><Text style={styles.headerNavLink}>Business Skills</Text></TouchableOpacity>
            <TouchableOpacity style={styles.loginButton} onPress={onShowLogin}><Text style={styles.loginText}>Log in</Text></TouchableOpacity>
            <TouchableOpacity style={styles.signupButton} onPress={onShowSignup}><Text style={styles.signupText}>Sign up</Text></TouchableOpacity>
          </>
        )}
      </View>
      {isMobile && mobileMenuOpen && (
        <View style={styles.mobileMenuOverlay}>
          <TouchableOpacity style={styles.mobileMenuClose} onPress={() => setMobileMenuOpen(false)}>
            <Ionicons name="close" size={36} color="#fff" />
          </TouchableOpacity>
          <View style={styles.mobileMenuContent}>
            <TouchableOpacity onPress={() => setMobileMenuOpen(false)}>
              <Text style={styles.mobileMenuLink}>Explore</Text>
            </TouchableOpacity>
            <TextInput style={styles.mobileMenuSearch} placeholder="Learn anything: Idea generation, development & more." placeholderTextColor="#888" />
            <TouchableOpacity onPress={() => setMobileMenuOpen(false)}>
              <Text style={styles.mobileMenuNavLink}>Boot Camps</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={() => setMobileMenuOpen(false)}>
              <Text style={styles.mobileMenuNavLink}>Business Skills</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.mobileMenuLogin} onPress={onShowLogin}>
              <Text style={styles.mobileMenuLoginText}>Log in</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.mobileMenuSignup} onPress={onShowSignup}>
              <Text style={styles.mobileMenuSignupText}>Sign up</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e5e5',
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
    width: '100%',
    maxWidth: 1200,
    alignSelf: 'center',
  },
  logo: {
    width: 36,
    height: 36,
    marginRight: 8,
  },
  headerTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#111827',
    marginRight: 20,
    letterSpacing: -0.5,
  },
  headerLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 20,
    fontWeight: '600',
    letterSpacing: 0.2,
  },
  searchInput: {
    flex: 1,
    height: 42,
    backgroundColor: '#f8f9fa',
    borderRadius: 21,
    paddingHorizontal: 18,
    fontSize: 16,
    marginRight: 16,
    borderWidth: 1,
    borderColor: '#e5e5e5',
    fontWeight: '400',
  },
  headerNavLink: {
    fontSize: 16,
    color: '#111827',
    marginRight: 16,
    fontWeight: '600',
    letterSpacing: 0.1,
  },
  loginButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#1976d2',
    marginRight: 10,
  },
  loginText: {
    color: '#1976d2',
    fontWeight: '600',
    fontSize: 16,
  },
  signupButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
    backgroundColor: '#1976d2',
    marginRight: 12,
  },
  signupText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
  },
  mobileMenuOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    backgroundColor: 'rgba(17,24,39,0.97)',
    zIndex: 100,
    paddingTop: 0,
    paddingHorizontal: 0,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  mobileMenuClose: {
    position: 'absolute',
    top: 18,
    right: 18,
    zIndex: 101,
  },
  mobileMenuContent: {
    marginTop: 64,
    width: '100%',
    paddingHorizontal: 24,
  },
  mobileMenuLink: {
    color: '#fff',
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 28,
    textAlign: 'left',
  },
  mobileMenuSearch: {
    backgroundColor: '#fff',
    color: '#111827',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 10,
    fontSize: 16,
    marginBottom: 28,
    width: '100%',
  },
  mobileMenuNavLink: {
    color: '#fff',
    fontSize: 20,
    marginBottom: 24,
    textAlign: 'left',
  },
  mobileMenuLogin: {
    backgroundColor: '#fff',
    borderRadius: 6,
    paddingVertical: 10,
    marginBottom: 16,
    alignItems: 'center',
  },
  mobileMenuLoginText: {
    color: '#1976d2',
    fontWeight: 'bold',
    fontSize: 16,
  },
  mobileMenuSignup: {
    backgroundColor: '#1976d2',
    borderRadius: 6,
    paddingVertical: 10,
    alignItems: 'center',
  },
  mobileMenuSignupText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
}); 