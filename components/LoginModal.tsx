import React, { useState } from 'react';
import { Platform, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { ThemedText } from './ThemedText';
import { ThemedView } from './ThemedView';

interface LoginModalProps {
  open: boolean;
  onClose: () => void;
}

export default function LoginModal({ open, onClose }: LoginModalProps) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  if (!open) return null;

  if (Platform.OS === 'web') {
    return (
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'rgba(0,0,0,0.4)',
        zIndex: 1000,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <div style={{
          background: '#fff',
          borderRadius: 12,
          padding: 0,
          minWidth: 500,
          maxWidth: 500,
          alignItems: 'center',
          boxShadow: '0 4px 32px rgba(0,0,0,0.15)',
          display: 'flex',
          flexDirection: 'column',
          maxHeight: '90vh',
        }}>
          <div style={{ width: '100%', maxHeight: '90vh', overflowY: 'auto', padding: 20, boxSizing: 'border-box', display: 'flex', flexDirection: 'column', alignItems: 'center', margin: '0 auto' }}>
            <div style={{ marginBottom: 18, textAlign: 'center' }}>
              <ThemedText type="title" style={{ color: '#222', fontWeight: 'bold', fontSize: 22 }}>Login</ThemedText>
              <ThemedText style={{ color: '#888', fontSize: 15, marginTop: 8, display: 'block' }}>Enter your credentials to log in to your account.</ThemedText>
            </div>
            <TextInput
              style={[styles.input, { width: 440 }]}
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
            />
            <TextInput
              style={[styles.input, { width: 440 }]}
              placeholder="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
            <TouchableOpacity style={styles.button}>
              <ThemedText style={styles.buttonText}>Login</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <ThemedText type="link">Close</ThemedText>
            </TouchableOpacity>
          </div>
        </div>
      </div>
    );
  }

  return (
    <View style={styles.nativeOverlay}>
      <ThemedView style={[styles.nativeModal, { minWidth: 260, maxWidth: 340 }]}>
        <ScrollView contentContainerStyle={{ alignItems: 'center', padding: 20, justifyContent: 'center' }} style={{ width: '100%' }}>
          <View style={{ marginBottom: 18, alignItems: 'center', width: '100%' }}>
            <ThemedText type="title" style={{ color: '#222', fontWeight: 'bold', fontSize: 22 }}>Login</ThemedText>
            <ThemedText style={{ color: '#888', fontSize: 15, marginTop: 8, textAlign: 'center' }}>Enter your credentials to log in to your account.</ThemedText>
          </View>
          <View style={{ width: '100%', alignItems: 'center' }}>
            <TextInput
              style={[styles.input, { width: 440 }]}
              placeholder="Email"
              value={email}
              onChangeText={setEmail}
              autoCapitalize="none"
              keyboardType="email-address"
            />
            <TextInput
              style={[styles.input, { width: 440 }]}
              placeholder="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry
            />
            <TouchableOpacity style={styles.button}>
              <ThemedText style={styles.buttonText}>Login</ThemedText>
            </TouchableOpacity>
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <ThemedText type="link">Close</ThemedText>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </ThemedView>
    </View>
  );
}

const styles = StyleSheet.create({
  nativeOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nativeModal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 32,
    minWidth: 320,
    maxWidth: 400,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  input: {
    width: 240,
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: '#ccc',
    backgroundColor: '#fff',
  },
  button: {
    width: 240,
    backgroundColor: '#007AFF',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  closeButton: {
    marginTop: 24,
    alignSelf: 'center',
  },
}); 